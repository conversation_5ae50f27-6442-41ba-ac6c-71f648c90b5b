{% extends "base.html" %}

{% block title %}Camera Map - Surveillance System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-map-marker-alt"></i> Camera Locations
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-globe"></i> Interactive Map
                </h5>
            </div>
            <div class="card-body p-0">
                <div id="map"></div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Camera Locations List
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>Camera</th>
                                <th>Location</th>
                                <th>Coordinates</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="camera-list">
                            <!-- Will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Camera View Modal -->
<div class="modal fade" id="cameraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cameraModalLabel">Camera View</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="cameraModalImage" src="" alt="Camera Feed" class="img-fluid">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-info" onclick="takeSnapshotFromModal()">
                    <i class="fas fa-camera"></i> Take Snapshot
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Camera data from server
const cameras = {{ cameras | safe }};
let map;
let currentCameraId = null;

// Initialize map
function initMap() {
    // Default center (Riyadh, Saudi Arabia)
    const defaultCenter = [24.7136, 46.6753];
    
    map = L.map('map').setView(defaultCenter, 10);
    
    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    // Add camera markers
    cameras.forEach(camera => {
        if (camera.latitude && camera.longitude) {
            addCameraMarker(camera);
        }
    });
    
    // Fit map to show all cameras if any exist
    if (cameras.length > 0) {
        const group = new L.featureGroup(map._layers);
        if (Object.keys(group._layers).length > 0) {
            map.fitBounds(group.getBounds().pad(0.1));
        }
    }
}

function addCameraMarker(camera) {
    const statusColor = getMarkerColor(camera.status);
    
    // Create custom icon
    const icon = L.divIcon({
        className: 'camera-marker',
        html: `<div class="marker-icon" style="background-color: ${statusColor};">
                   <i class="fas fa-video"></i>
               </div>`,
        iconSize: [30, 30],
        iconAnchor: [15, 15]
    });
    
    const marker = L.marker([camera.latitude, camera.longitude], { icon: icon })
        .addTo(map);
    
    // Create popup content
    const popupContent = `
        <div class="camera-popup">
            <h6>${camera.name}</h6>
            <p class="mb-2">${camera.location_name || 'No location name'}</p>
            <p class="mb-2">
                <span class="badge bg-${getStatusBootstrapColor(camera.status)}">
                    ${camera.status.charAt(0).toUpperCase() + camera.status.slice(1)}
                </span>
            </p>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-primary" onclick="viewCameraOnMap(${camera.id})">
                    <i class="fas fa-eye"></i> View
                </button>
                <button class="btn btn-info" onclick="takeSnapshotFromMap(${camera.id})">
                    <i class="fas fa-camera"></i> Snapshot
                </button>
            </div>
        </div>
    `;
    
    marker.bindPopup(popupContent);
}

function getMarkerColor(status) {
    switch (status) {
        case 'active': return '#28a745';
        case 'error': return '#dc3545';
        case 'connecting': return '#ffc107';
        default: return '#6c757d';
    }
}

function getStatusBootstrapColor(status) {
    switch (status) {
        case 'active': return 'success';
        case 'error': return 'danger';
        case 'connecting': return 'warning';
        default: return 'secondary';
    }
}

function viewCameraOnMap(cameraId) {
    currentCameraId = cameraId;
    const camera = cameras.find(c => c.id === cameraId);
    
    if (camera) {
        document.getElementById('cameraModalLabel').textContent = camera.name;
        document.getElementById('cameraModalImage').src = `/video_feed/${cameraId}`;
        
        const modal = new bootstrap.Modal(document.getElementById('cameraModal'));
        modal.show();
    }
}

function takeSnapshotFromMap(cameraId) {
    cameraControls.snapshot(cameraId)
        .then(data => {
            if (data.success) {
                showNotification('Snapshot taken successfully', 'success');
            } else {
                showNotification('Failed to take snapshot: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to take snapshot', 'danger');
        });
}

function takeSnapshotFromModal() {
    if (currentCameraId) {
        takeSnapshotFromMap(currentCameraId);
    }
}

function populateCameraList() {
    const tbody = document.getElementById('camera-list');
    tbody.innerHTML = '';
    
    cameras.forEach(camera => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <strong>${camera.name}</strong>
                ${camera.description ? `<br><small class="text-muted">${camera.description}</small>` : ''}
            </td>
            <td>${camera.location_name || '<span class="text-muted">No location</span>'}</td>
            <td>
                ${camera.latitude && camera.longitude ? 
                    `${camera.latitude.toFixed(6)}, ${camera.longitude.toFixed(6)}` : 
                    '<span class="text-muted">No coordinates</span>'}
            </td>
            <td>
                <span class="badge bg-${getStatusBootstrapColor(camera.status)}">
                    ${camera.status.charAt(0).toUpperCase() + camera.status.slice(1)}
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    ${camera.latitude && camera.longitude ? 
                        `<button class="btn btn-outline-primary" onclick="focusOnCamera(${camera.latitude}, ${camera.longitude})">
                            <i class="fas fa-crosshairs"></i> Locate
                        </button>` : ''}
                    <button class="btn btn-outline-info" onclick="viewCameraOnMap(${camera.id})">
                        <i class="fas fa-eye"></i> View
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function focusOnCamera(lat, lng) {
    map.setView([lat, lng], 15);
    
    // Add a temporary highlight circle
    const circle = L.circle([lat, lng], {
        color: '#007bff',
        fillColor: '#007bff',
        fillOpacity: 0.2,
        radius: 100
    }).addTo(map);
    
    // Remove the circle after 3 seconds
    setTimeout(() => {
        map.removeLayer(circle);
    }, 3000);
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initMap();
    populateCameraList();
});

// Add custom CSS for camera markers
const style = document.createElement('style');
style.textContent = `
    .camera-marker {
        background: none !important;
        border: none !important;
    }
    
    .marker-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        border: 2px solid white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    }
    
    .camera-popup {
        text-align: center;
        min-width: 200px;
    }
    
    .camera-popup h6 {
        margin-bottom: 10px;
        color: #495057;
    }
    
    #map {
        height: 500px;
        border-radius: 15px;
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
