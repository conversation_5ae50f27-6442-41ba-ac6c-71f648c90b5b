{% extends "base.html" %}

{% block title %}Cameras - Surveillance System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-camera"></i> Camera Management
            </h1>
            {% if current_user.has_permission('manage') %}
            <a href="{{ url_for('add_camera') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Camera
            </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Camera List
                </h5>
            </div>
            <div class="card-body">
                {% if cameras %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Location</th>
                                <th>Status</th>
                                <th>Last Seen</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for camera in cameras %}
                            <tr>
                                <td>
                                    <strong>{{ camera.name }}</strong>
                                    {% if camera.description %}
                                    <br><small class="text-muted">{{ camera.description }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        {{ camera.camera_type.value.upper() }}
                                    </span>
                                </td>
                                <td>
                                    {% if camera.location_name %}
                                        <i class="fas fa-map-marker-alt"></i> {{ camera.location_name }}
                                    {% else %}
                                        <span class="text-muted">No location</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if camera.status.value == 'active' else 'danger' if camera.status.value == 'error' else 'secondary' }}">
                                        {{ camera.status.value.title() }}
                                    </span>
                                    {% if camera.motion_detection %}
                                    <br><small class="text-info"><i class="fas fa-running"></i> Motion Detection</small>
                                    {% endif %}
                                    {% if camera.is_recording %}
                                    <br><small class="text-danger"><i class="fas fa-record-vinyl"></i> Recording</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if camera.last_seen %}
                                        <small>{{ camera.last_seen.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                                    {% else %}
                                        <span class="text-muted">Never</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <!-- Start/Stop Camera -->
                                        {% if camera.status.value != 'active' %}
                                        <button class="btn btn-success" onclick="startCamera({{ camera.id }})" title="Start Camera">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        {% else %}
                                        <button class="btn btn-danger" onclick="stopCamera({{ camera.id }})" title="Stop Camera">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                        {% endif %}
                                        
                                        <!-- View Camera -->
                                        <button class="btn btn-info" onclick="viewCamera({{ camera.id }})" title="View Camera">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        <!-- Take Snapshot -->
                                        <button class="btn btn-secondary" onclick="takeSnapshot({{ camera.id }})" title="Take Snapshot">
                                            <i class="fas fa-camera"></i>
                                        </button>
                                        
                                        {% if current_user.has_permission('manage') %}
                                        <!-- Edit Camera -->
                                        <a href="{{ url_for('edit_camera', camera_id=camera.id) }}" class="btn btn-warning" title="Edit Camera">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <!-- Delete Camera -->
                                        <button class="btn btn-danger" onclick="deleteCamera({{ camera.id }}, '{{ camera.name }}')" title="Delete Camera">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No cameras configured</h5>
                    <p class="text-muted">Add your first camera to start monitoring</p>
                    {% if current_user.has_permission('manage') %}
                    <a href="{{ url_for('add_camera') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Camera
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Camera View Modal -->
<div class="modal fade" id="cameraViewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cameraViewModalLabel">Camera View</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="cameraViewImage" src="" alt="Camera Feed" class="img-fluid" style="max-height: 400px;">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="openFullscreen()">
                    <i class="fas fa-expand"></i> Fullscreen
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentCameraId = null;

function startCamera(cameraId) {
    fetch(`/cameras/${cameraId}/start`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Camera started successfully', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('Failed to start camera: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Failed to start camera', 'danger');
    });
}

function stopCamera(cameraId) {
    fetch(`/cameras/${cameraId}/stop`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Camera stopped successfully', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('Failed to stop camera: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Failed to stop camera', 'danger');
    });
}

function viewCamera(cameraId) {
    currentCameraId = cameraId;
    const img = document.getElementById('cameraViewImage');
    img.src = `/video_feed/${cameraId}`;
    const modal = new bootstrap.Modal(document.getElementById('cameraViewModal'));
    modal.show();
}

function takeSnapshot(cameraId) {
    fetch(`/api/cameras/${cameraId}/snapshot`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Snapshot taken successfully', 'success');
        } else {
            showAlert('Failed to take snapshot: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Failed to take snapshot', 'danger');
    });
}

function deleteCamera(cameraId, cameraName) {
    if (confirm(`Are you sure you want to delete camera "${cameraName}"? This action cannot be undone.`)) {
        fetch(`/cameras/${cameraId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Camera deleted successfully', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('Failed to delete camera: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Failed to delete camera', 'danger');
        });
    }
}

function openFullscreen() {
    if (currentCameraId) {
        const img = document.getElementById('cameraViewImage');
        if (img.requestFullscreen) {
            img.requestFullscreen();
        } else if (img.webkitRequestFullscreen) {
            img.webkitRequestFullscreen();
        } else if (img.msRequestFullscreen) {
            img.msRequestFullscreen();
        }
    }
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
