{% extends "base.html" %}

{% block title %}Add Camera - Surveillance System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-plus"></i> Add New Camera
            </h1>
            <a href="{{ url_for('cameras') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Cameras
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog"></i> Camera Configuration
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="cameraForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-tag"></i> Camera Name *
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="form-text">Enter a descriptive name for the camera</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="camera_type" class="form-label">
                                    <i class="fas fa-video"></i> Camera Type *
                                </label>
                                <select class="form-select" id="camera_type" name="camera_type" required onchange="updateConnectionFields()">
                                    <option value="">Select camera type</option>
                                    {% for camera_type in camera_types %}
                                    <option value="{{ camera_type.value }}">{{ camera_type.value.upper() }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left"></i> Description
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="connection_string" class="form-label">
                            <i class="fas fa-link"></i> Connection String *
                        </label>
                        <input type="text" class="form-control" id="connection_string" name="connection_string" required>
                        <div class="form-text" id="connection_help">
                            Enter the connection details for your camera
                        </div>
                    </div>
                    
                    <div class="row" id="auth_fields" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user"></i> Username
                                </label>
                                <input type="text" class="form-control" id="username" name="username">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock"></i> Password
                                </label>
                                <input type="password" class="form-control" id="password" name="password">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row" id="network_fields" style="display: none;">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="ip_address" class="form-label">
                                    <i class="fas fa-network-wired"></i> IP Address
                                </label>
                                <input type="text" class="form-control" id="ip_address" name="ip_address" placeholder="*************">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="port" class="form-label">
                                    <i class="fas fa-plug"></i> Port
                                </label>
                                <input type="number" class="form-control" id="port" name="port" placeholder="554">
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6><i class="fas fa-map-marker-alt"></i> Location Information</h6>
                    
                    <div class="mb-3">
                        <label for="location_name" class="form-label">
                            <i class="fas fa-map-pin"></i> Location Name
                        </label>
                        <input type="text" class="form-control" id="location_name" name="location_name" placeholder="e.g., Main Entrance, Parking Lot">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">
                                    <i class="fas fa-globe"></i> Latitude
                                </label>
                                <input type="number" step="any" class="form-control" id="latitude" name="latitude" placeholder="24.7136">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">
                                    <i class="fas fa-globe"></i> Longitude
                                </label>
                                <input type="number" step="any" class="form-control" id="longitude" name="longitude" placeholder="46.6753">
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6><i class="fas fa-cogs"></i> Camera Settings</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="motion_detection" name="motion_detection">
                                <label class="form-check-label" for="motion_detection">
                                    <i class="fas fa-running"></i> Enable Motion Detection
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_recording" name="is_recording">
                                <label class="form-check-label" for="is_recording">
                                    <i class="fas fa-record-vinyl"></i> Enable Recording
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('cameras') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="button" class="btn btn-info" onclick="testConnection()">
                            <i class="fas fa-plug"></i> Test Connection
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Add Camera
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Connection Examples
                </h5>
            </div>
            <div class="card-body">
                <div id="connection_examples">
                    <p class="text-muted">Select a camera type to see connection examples</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle"></i> Help
                </h5>
            </div>
            <div class="card-body">
                <h6>Supported Camera Types:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-video text-primary"></i> <strong>RTSP:</strong> Network cameras with RTSP protocol</li>
                    <li><i class="fas fa-usb-port text-success"></i> <strong>USB:</strong> Local USB webcams</li>
                    <li><i class="fas fa-camera text-info"></i> <strong>Dahua:</strong> Dahua IP cameras</li>
                    <li><i class="fas fa-video text-warning"></i> <strong>Hikvision:</strong> Hikvision IP cameras</li>
                    <li><i class="fas fa-network-wired text-secondary"></i> <strong>ONVIF:</strong> ONVIF-compliant cameras</li>
                    <li><i class="fas fa-globe text-danger"></i> <strong>HTTP:</strong> HTTP/MJPEG streams</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
const connectionExamples = {
    'rtsp': {
        example: 'rtsp://username:password@*************:554/stream1',
        help: 'RTSP URL format: rtsp://[username:password@]host[:port]/path',
        showAuth: true,
        showNetwork: true
    },
    'usb': {
        example: '0',
        help: 'USB device number (usually 0 for first camera, 1 for second, etc.)',
        showAuth: false,
        showNetwork: false
    },
    'dahua': {
        example: 'rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0',
        help: 'Dahua RTSP format with channel and subtype parameters',
        showAuth: true,
        showNetwork: true
    },
    'hikvision': {
        example: 'rtsp://admin:password@*************:554/Streaming/Channels/101',
        help: 'Hikvision RTSP format with channel number',
        showAuth: true,
        showNetwork: true
    },
    'onvif': {
        example: 'onvif://*************:80',
        help: 'ONVIF camera address (will auto-discover stream URL)',
        showAuth: true,
        showNetwork: true
    },
    'http': {
        example: 'http://*************:8080/video',
        help: 'HTTP/MJPEG stream URL',
        showAuth: false,
        showNetwork: true
    }
};

function updateConnectionFields() {
    const cameraType = document.getElementById('camera_type').value;
    const connectionString = document.getElementById('connection_string');
    const connectionHelp = document.getElementById('connection_help');
    const authFields = document.getElementById('auth_fields');
    const networkFields = document.getElementById('network_fields');
    const examplesDiv = document.getElementById('connection_examples');
    
    if (cameraType && connectionExamples[cameraType]) {
        const config = connectionExamples[cameraType];
        
        // Update connection string placeholder and help
        connectionString.placeholder = config.example;
        connectionHelp.textContent = config.help;
        
        // Show/hide fields based on camera type
        authFields.style.display = config.showAuth ? 'block' : 'none';
        networkFields.style.display = config.showNetwork ? 'block' : 'none';
        
        // Update examples
        examplesDiv.innerHTML = `
            <h6>Example:</h6>
            <code>${config.example}</code>
            <p class="mt-2 text-muted small">${config.help}</p>
        `;
    } else {
        authFields.style.display = 'none';
        networkFields.style.display = 'none';
        examplesDiv.innerHTML = '<p class="text-muted">Select a camera type to see connection examples</p>';
    }
}

function testConnection() {
    const formData = new FormData(document.getElementById('cameraForm'));
    const testData = {
        camera_type: formData.get('camera_type'),
        connection_string: formData.get('connection_string'),
        username: formData.get('username'),
        password: formData.get('password'),
        ip_address: formData.get('ip_address'),
        port: formData.get('port')
    };
    
    if (!testData.camera_type || !testData.connection_string) {
        alert('Please select camera type and enter connection string first');
        return;
    }
    
    // Show loading state
    const testBtn = event.target;
    const originalText = testBtn.innerHTML;
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    testBtn.disabled = true;
    
    // Simulate connection test (in real implementation, this would call an API endpoint)
    setTimeout(() => {
        testBtn.innerHTML = originalText;
        testBtn.disabled = false;
        alert('Connection test completed. Check the logs for details.');
    }, 2000);
}

// Initialize form
document.addEventListener('DOMContentLoaded', function() {
    updateConnectionFields();
});
</script>
{% endblock %}
