from flask import Flask, render_template, Response, request, jsonify, redirect, url_for, flash
from flask_login import LoginManager, login_required, current_user
from flask_socketio import SocketIO, emit
import os
import logging
from datetime import datetime
import json

# Import our modules
from config import config
from models import db, User, Camera, CameraType, CameraStatus, UserRole
from auth import auth_bp, require_permission
from camera_manager import camera_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)
    
    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Initialize SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*")
    
    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    # Create directories
    os.makedirs(app.config['RECORDING_PATH'], exist_ok=True)
    os.makedirs(app.config['SNAPSHOT_PATH'], exist_ok=True)
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    # Routes
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('auth.login'))
    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        cameras = Camera.query.all()
        active_cameras = Camera.query.filter_by(status=CameraStatus.ACTIVE).count()
        total_cameras = Camera.query.count()
        
        return render_template('dashboard.html', 
                             cameras=cameras,
                             active_cameras=active_cameras,
                             total_cameras=total_cameras)
    
    @app.route('/cameras')
    @login_required
    def cameras():
        cameras = Camera.query.all()
        return render_template('cameras.html', cameras=cameras)
    
    @app.route('/cameras/add', methods=['GET', 'POST'])
    @login_required
    @require_permission('manage')
    def add_camera():
        if request.method == 'POST':
            try:
                camera = Camera(
                    name=request.form['name'],
                    description=request.form.get('description', ''),
                    camera_type=CameraType(request.form['camera_type']),
                    connection_string=request.form['connection_string'],
                    username=request.form.get('username'),
                    password=request.form.get('password'),
                    ip_address=request.form.get('ip_address'),
                    port=int(request.form['port']) if request.form.get('port') else None,
                    latitude=float(request.form['latitude']) if request.form.get('latitude') else None,
                    longitude=float(request.form['longitude']) if request.form.get('longitude') else None,
                    location_name=request.form.get('location_name'),
                    motion_detection=bool(request.form.get('motion_detection')),
                    is_recording=bool(request.form.get('is_recording')),
                    created_by=current_user.id
                )
                
                db.session.add(camera)
                db.session.commit()
                
                flash('Camera added successfully!', 'success')
                return redirect(url_for('cameras'))
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error adding camera: {str(e)}")
                flash('Failed to add camera. Please check your input.', 'error')
        
        return render_template('add_camera.html', camera_types=CameraType)
    
    @app.route('/cameras/<int:camera_id>/edit', methods=['GET', 'POST'])
    @login_required
    @require_permission('manage')
    def edit_camera(camera_id):
        camera = Camera.query.get_or_404(camera_id)
        
        if request.method == 'POST':
            try:
                camera.name = request.form['name']
                camera.description = request.form.get('description', '')
                camera.camera_type = CameraType(request.form['camera_type'])
                camera.connection_string = request.form['connection_string']
                camera.username = request.form.get('username')
                camera.password = request.form.get('password')
                camera.ip_address = request.form.get('ip_address')
                camera.port = int(request.form['port']) if request.form.get('port') else None
                camera.latitude = float(request.form['latitude']) if request.form.get('latitude') else None
                camera.longitude = float(request.form['longitude']) if request.form.get('longitude') else None
                camera.location_name = request.form.get('location_name')
                camera.motion_detection = bool(request.form.get('motion_detection'))
                camera.is_recording = bool(request.form.get('is_recording'))
                camera.updated_at = datetime.utcnow()
                
                db.session.commit()
                
                flash('Camera updated successfully!', 'success')
                return redirect(url_for('cameras'))
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error updating camera: {str(e)}")
                flash('Failed to update camera. Please check your input.', 'error')
        
        return render_template('edit_camera.html', camera=camera, camera_types=CameraType)
    
    @app.route('/cameras/<int:camera_id>/delete', methods=['POST'])
    @login_required
    @require_permission('manage')
    def delete_camera(camera_id):
        try:
            camera = Camera.query.get_or_404(camera_id)
            
            # Stop camera stream if running
            camera_manager.stop_camera(camera_id)
            
            db.session.delete(camera)
            db.session.commit()
            
            return jsonify({'success': True, 'message': 'Camera deleted successfully'})
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting camera: {str(e)}")
            return jsonify({'success': False, 'message': 'Failed to delete camera'}), 500

    @app.route('/cameras/<int:camera_id>/start', methods=['POST'])
    @login_required
    def start_camera(camera_id):
        try:
            if camera_manager.start_camera(camera_id):
                return jsonify({'success': True, 'message': 'Camera started successfully'})
            else:
                return jsonify({'success': False, 'message': 'Failed to start camera'}), 500
        except Exception as e:
            logger.error(f"Error starting camera: {str(e)}")
            return jsonify({'success': False, 'message': 'Failed to start camera'}), 500

    @app.route('/cameras/<int:camera_id>/stop', methods=['POST'])
    @login_required
    def stop_camera(camera_id):
        try:
            if camera_manager.stop_camera(camera_id):
                return jsonify({'success': True, 'message': 'Camera stopped successfully'})
            else:
                return jsonify({'success': False, 'message': 'Failed to stop camera'}), 500
        except Exception as e:
            logger.error(f"Error stopping camera: {str(e)}")
            return jsonify({'success': False, 'message': 'Failed to stop camera'}), 500

    @app.route('/video_feed/<int:camera_id>')
    @login_required
    def video_feed(camera_id):
        def generate():
            while True:
                frame = camera_manager.get_frame(camera_id)
                if frame:
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
                else:
                    # Send a placeholder image if no frame available
                    import cv2
                    import numpy as np
                    placeholder = np.zeros((480, 640, 3), dtype=np.uint8)
                    cv2.putText(placeholder, 'Camera Offline', (200, 240),
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                    ret, buffer = cv2.imencode('.jpg', placeholder)
                    if ret:
                        yield (b'--frame\r\n'
                               b'Content-Type: image/jpeg\r\n\r\n' + buffer.tobytes() + b'\r\n')

                import time
                time.sleep(0.1)  # Limit frame rate

        return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

    @app.route('/map')
    @login_required
    def map_view():
        cameras = Camera.query.filter(Camera.latitude.isnot(None), Camera.longitude.isnot(None)).all()
        cameras_data = [camera.to_dict() for camera in cameras]
        return render_template('map.html', cameras=json.dumps(cameras_data))

    @app.route('/api/cameras')
    @login_required
    def api_cameras():
        cameras = Camera.query.all()
        return jsonify([camera.to_dict() for camera in cameras])

    @app.route('/api/cameras/<int:camera_id>/snapshot', methods=['POST'])
    @login_required
    def take_snapshot(camera_id):
        try:
            frame = camera_manager.get_frame(camera_id)
            if frame:
                # Save snapshot logic would go here
                return jsonify({'success': True, 'message': 'Snapshot taken successfully'})
            else:
                return jsonify({'success': False, 'message': 'Camera not available'}), 400
        except Exception as e:
            logger.error(f"Error taking snapshot: {str(e)}")
            return jsonify({'success': False, 'message': 'Failed to take snapshot'}), 500

    # SocketIO events
    @socketio.on('connect')
    def handle_connect():
        if current_user.is_authenticated:
            emit('status', {'message': 'Connected to surveillance system'})
        else:
            return False  # Reject connection

    @socketio.on('disconnect')
    def handle_disconnect():
        logger.info(f'User {current_user.username if current_user.is_authenticated else "Unknown"} disconnected')

    @socketio.on('camera_status_request')
    def handle_camera_status_request(data):
        if current_user.is_authenticated:
            cameras = Camera.query.all()
            camera_status = {}
            for camera in cameras:
                camera_status[camera.id] = {
                    'status': camera.status.value,
                    'last_seen': camera.last_seen.isoformat() if camera.last_seen else None
                }
            emit('camera_status_update', camera_status)

    # Error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500

    # Create database tables
    with app.app_context():
        db.create_all()

        # Create default admin user if no users exist
        if User.query.count() == 0:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role=UserRole.ADMIN
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            logger.info("Default admin user created: admin/admin123")

    return app, socketio

if __name__ == '__main__':
    app, socketio = create_app()
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
