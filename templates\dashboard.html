{% extends "base.html" %}

{% block title %}Dashboard - Surveillance System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_cameras }}</h4>
                        <p class="mb-0">Total Cameras</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-camera fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ active_cameras }}</h4>
                        <p class="mb-0">Active Cameras</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-play-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_cameras - active_cameras }}</h4>
                        <p class="mb-0">Offline Cameras</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-pause-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="recording-count">0</h4>
                        <p class="mb-0">Recording</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-record-vinyl fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Camera Grid -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-video"></i> Live Camera Feeds
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setGridSize(1)">1x1</button>
                    <button type="button" class="btn btn-outline-primary btn-sm active" onclick="setGridSize(2)">2x2</button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setGridSize(3)">3x3</button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setGridSize(4)">4x4</button>
                </div>
            </div>
            <div class="card-body">
                <div id="camera-grid" class="camera-grid grid-2x2">
                    {% for camera in cameras %}
                    <div class="camera-item" data-camera-id="{{ camera.id }}">
                        <div class="camera-header">
                            <h6 class="camera-title">{{ camera.name }}</h6>
                            <div class="camera-controls">
                                <span class="badge bg-{{ 'success' if camera.status.value == 'active' else 'secondary' }}">
                                    {{ camera.status.value.title() }}
                                </span>
                                <div class="btn-group btn-group-sm">
                                    {% if camera.status.value != 'active' %}
                                    <button class="btn btn-success btn-sm" onclick="startCamera({{ camera.id }})">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    {% else %}
                                    <button class="btn btn-danger btn-sm" onclick="stopCamera({{ camera.id }})">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-info btn-sm" onclick="takeSnapshot({{ camera.id }})">
                                        <i class="fas fa-camera"></i>
                                    </button>
                                    <button class="btn btn-primary btn-sm" onclick="openFullscreen({{ camera.id }})">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="camera-video">
                            {% if camera.status.value == 'active' %}
                            <img src="{{ url_for('video_feed', camera_id=camera.id) }}" 
                                 alt="{{ camera.name }}" 
                                 class="camera-stream"
                                 onerror="this.src='{{ url_for('static', filename='img/camera-offline.png') }}'">
                            {% else %}
                            <div class="camera-offline">
                                <i class="fas fa-video-slash fa-3x"></i>
                                <p>Camera Offline</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="camera-info">
                            <small class="text-muted">
                                {{ camera.location_name or 'No location' }} | 
                                {{ camera.camera_type.value.upper() }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if cameras|length == 0 %}
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No cameras configured</h5>
                        <p class="text-muted">Add your first camera to start monitoring</p>
                        {% if current_user.has_permission('manage') %}
                        <a href="{{ url_for('add_camera') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Camera
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fullscreen Modal -->
<div class="modal fade" id="fullscreenModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fullscreenModalLabel">Camera View</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <img id="fullscreenImage" src="" alt="Camera Feed" class="w-100 h-100" style="object-fit: contain;">
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Camera control functions
function startCamera(cameraId) {
    fetch(`/cameras/${cameraId}/start`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to start camera: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to start camera');
    });
}

function stopCamera(cameraId) {
    fetch(`/cameras/${cameraId}/stop`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to stop camera: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to stop camera');
    });
}

function takeSnapshot(cameraId) {
    fetch(`/api/cameras/${cameraId}/snapshot`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Snapshot taken successfully');
        } else {
            alert('Failed to take snapshot: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to take snapshot');
    });
}

function openFullscreen(cameraId) {
    const img = document.getElementById('fullscreenImage');
    img.src = `/video_feed/${cameraId}`;
    const modal = new bootstrap.Modal(document.getElementById('fullscreenModal'));
    modal.show();
}

function setGridSize(size) {
    const grid = document.getElementById('camera-grid');
    grid.className = `camera-grid grid-${size}x${size}`;
    
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
}

// Update recording count
function updateRecordingCount() {
    const recordingCameras = document.querySelectorAll('[data-recording="true"]').length;
    document.getElementById('recording-count').textContent = recordingCameras;
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateRecordingCount();
    
    // Auto-refresh camera status every 30 seconds
    setInterval(() => {
        location.reload();
    }, 30000);
});
</script>
{% endblock %}
