#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المراقبة العربي البسيط
Simple Arabic Surveillance System
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session
import os

app = Flask(__name__)
app.secret_key = 'arabic-surveillance-2024'

# بيانات وهمية للاختبار
users = {'admin': 'admin123'}
cameras = [
    {'id': 1, 'name': 'كاميرا المدخل الرئيسي', 'type': 'RTSP', 'status': 'نشط', 'location': 'المدخل الرئيسي'},
    {'id': 2, 'name': 'كاميرا الموقف', 'type': 'USB', 'status': 'غير نشط', 'location': 'موقف السيارات'},
    {'id': 3, 'name': 'كاميرا الحديقة', 'type': 'داهوا', 'status': 'نشط', 'location': 'الحديقة الخلفية'},
]

# قالب تسجيل الدخول
LOGIN_HTML = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المراقبة العربي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 450px;
            width: 100%;
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        .login-body { padding: 2rem; }
        .form-control { 
            text-align: right; 
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
        }
        .system-icon { font-size: 4rem; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-header">
            <div class="system-icon">🎥</div>
            <h2 class="mb-3">نظام المراقبة العربي</h2>
            <p class="mb-0">يرجى تسجيل الدخول للمتابعة</p>
        </div>
        <div class="login-body">
            {% if error %}
                <div class="alert alert-danger text-center">{{ error }}</div>
            {% endif %}
            
            <form method="POST">
                <div class="mb-4">
                    <label class="form-label fw-bold">اسم المستخدم</label>
                    <input type="text" class="form-control" name="username" placeholder="أدخل اسم المستخدم" required>
                </div>
                <div class="mb-4">
                    <label class="form-label fw-bold">كلمة المرور</label>
                    <input type="password" class="form-control" name="password" placeholder="أدخل كلمة المرور" required>
                </div>
                <button type="submit" class="btn btn-primary btn-login w-100 mb-3">
                    تسجيل الدخول
                </button>
            </form>
            
            <div class="alert alert-info text-center">
                <strong>🔑 بيانات الدخول الافتراضية:</strong><br>
                <code>admin</code> / <code>admin123</code>
            </div>
        </div>
    </div>
</body>
</html>
"""

# قالب لوحة التحكم
DASHBOARD_HTML = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام المراقبة العربي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background-color: #f8f9fa; 
        }
        .navbar { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card { 
            border: none; 
            border-radius: 15px; 
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .card-header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            border-radius: 15px 15px 0 0 !important; 
            border: none;
        }
        .stat-card { 
            transition: all 0.3s ease; 
            cursor: pointer;
        }
        .stat-card:hover { 
            transform: translateY(-10px); 
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .camera-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .camera-preview {
            height: 200px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }
        .status-active { color: #28a745; }
        .status-inactive { color: #6c757d; }
        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="#">
                <i class="fas fa-video me-2"></i>نظام المراقبة العربي
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="{{ url_for('cameras') }}">
                    <i class="fas fa-camera me-1"></i>الكاميرات
                </a>
                <a class="nav-link" href="{{ url_for('add_camera') }}">
                    <i class="fas fa-plus me-1"></i>إضافة كاميرا
                </a>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- قسم الترحيب -->
        <div class="welcome-section text-center">
            <h1 class="display-4 mb-3">🎥 مرحباً بك في نظام المراقبة</h1>
            <p class="lead">نظام مراقبة متطور يدعم اللغة العربية بالكامل</p>
        </div>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-camera fa-3x mb-3"></i>
                        <h2 class="display-4">{{ total_cameras }}</h2>
                        <p class="mb-0 fs-5">إجمالي الكاميرات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-play-circle fa-3x mb-3"></i>
                        <h2 class="display-4">{{ active_cameras }}</h2>
                        <p class="mb-0 fs-5">الكاميرات النشطة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-white stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-pause-circle fa-3x mb-3"></i>
                        <h2 class="display-4">{{ offline_cameras }}</h2>
                        <p class="mb-0 fs-5">الكاميرات غير المتصلة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-user fa-3x mb-3"></i>
                        <h2 class="display-4">1</h2>
                        <p class="mb-0 fs-5">المستخدمين النشطين</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة الكاميرات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-video me-2"></i>الكاميرات المتاحة
                        </h4>
                    </div>
                    <div class="card-body">
                        {% if cameras %}
                            <div class="row">
                                {% for camera in cameras %}
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="camera-card">
                                        <div class="camera-preview">
                                            <i class="fas fa-video"></i>
                                        </div>
                                        <div class="p-3">
                                            <h5 class="card-title">{{ camera.name }}</h5>
                                            <div class="row mb-2">
                                                <div class="col-6">
                                                    <small class="text-muted">النوع:</small><br>
                                                    <span class="badge bg-secondary">{{ camera.type }}</span>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">الحالة:</small><br>
                                                    <span class="badge bg-{{ 'success' if camera.status == 'نشط' else 'secondary' }}">
                                                        {{ camera.status }}
                                                    </span>
                                                </div>
                                            </div>
                                            <p class="text-muted mb-3">
                                                <i class="fas fa-map-marker-alt me-1"></i>{{ camera.location }}
                                            </p>
                                            <div class="btn-group w-100">
                                                <button class="btn btn-primary btn-sm">
                                                    <i class="fas fa-eye"></i> عرض
                                                </button>
                                                <button class="btn btn-info btn-sm">
                                                    <i class="fas fa-camera"></i> صورة
                                                </button>
                                                <button class="btn btn-warning btn-sm">
                                                    <i class="fas fa-cog"></i> إعدادات
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-camera fa-5x text-muted mb-4"></i>
                                <h3 class="text-muted">لا توجد كاميرات مُكوّنة</h3>
                                <p class="text-muted fs-5">أضف أول كاميرا لبدء المراقبة</p>
                                <a href="{{ url_for('add_camera') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus me-2"></i>إضافة كاميرا جديدة
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثير التحميل
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
"""

# الصفحات
@app.route('/')
def index():
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    error = None
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if username in users and users[username] == password:
            session['logged_in'] = True
            session['username'] = username
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            error = 'اسم المستخدم أو كلمة المرور غير صحيحة'
    
    return render_template_string(LOGIN_HTML, error=error)

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'logged_in' not in session:
        return redirect(url_for('login'))
    
    total_cameras = len(cameras)
    active_cameras = len([c for c in cameras if c['status'] == 'نشط'])
    offline_cameras = total_cameras - active_cameras
    
    return render_template_string(DASHBOARD_HTML, 
                                cameras=cameras,
                                total_cameras=total_cameras,
                                active_cameras=active_cameras,
                                offline_cameras=offline_cameras)

@app.route('/cameras')
def cameras():
    if 'logged_in' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))

@app.route('/add_camera')
def add_camera():
    if 'logged_in' not in session:
        return redirect(url_for('login'))
    
    # إضافة كاميرا وهمية للعرض
    new_camera = {
        'id': len(cameras) + 1,
        'name': f'كاميرا جديدة {len(cameras) + 1}',
        'type': 'RTSP',
        'status': 'نشط',
        'location': 'موقع جديد'
    }
    cameras.append(new_camera)
    flash('تم إضافة الكاميرا بنجاح!', 'success')
    return redirect(url_for('dashboard'))

if __name__ == '__main__':
    print("🎥 نظام المراقبة العربي البسيط")
    print("=" * 50)
    print("📍 الخادم: http://localhost:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("🌐 اللغة: العربية الكاملة")
    print("✨ التصميم: متجاوب وجميل")
    print("=" * 50)
    print("🚀 النظام جاهز للاستخدام!")
    print()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
