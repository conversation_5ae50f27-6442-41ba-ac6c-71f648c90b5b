#!/usr/bin/env python3
"""
Surveillance System Runner
A simple script to run the surveillance system with proper configuration.
"""

import os
import sys
from app import create_app

def main():
    """Main function to run the surveillance system."""
    
    # Set environment if not already set
    if not os.environ.get('FLASK_ENV'):
        os.environ['FLASK_ENV'] = 'development'
    
    # Create the Flask app
    try:
        app, socketio = create_app()
        
        print("🎥 Starting Surveillance System...")
        print("=" * 50)
        print(f"📍 Server: http://localhost:5000")
        print(f"👤 Default Login: admin / admin123")
        print(f"🔧 Environment: {os.environ.get('FLASK_ENV', 'development')}")
        print("=" * 50)
        print("📝 Press Ctrl+C to stop the server")
        print()
        
        # Run the application
        socketio.run(
            app,
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=True,
            log_output=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting server: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
