{% extends "base.html" %}

{% block title %}Edit Camera - Surveillance System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-edit"></i> Edit Camera: {{ camera.name }}
            </h1>
            <a href="{{ url_for('cameras') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Cameras
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog"></i> Camera Configuration
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="cameraForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-tag"></i> Camera Name *
                                </label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ camera.name }}" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="camera_type" class="form-label">
                                    <i class="fas fa-video"></i> Camera Type *
                                </label>
                                <select class="form-select" id="camera_type" name="camera_type" required onchange="updateConnectionFields()">
                                    {% for camera_type in camera_types %}
                                    <option value="{{ camera_type.value }}" {{ 'selected' if camera.camera_type == camera_type else '' }}>
                                        {{ camera_type.value.upper() }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left"></i> Description
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="2">{{ camera.description or '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="connection_string" class="form-label">
                            <i class="fas fa-link"></i> Connection String *
                        </label>
                        <input type="text" class="form-control" id="connection_string" name="connection_string" value="{{ camera.connection_string }}" required>
                        <div class="form-text" id="connection_help">
                            Enter the connection details for your camera
                        </div>
                    </div>
                    
                    <div class="row" id="auth_fields">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user"></i> Username
                                </label>
                                <input type="text" class="form-control" id="username" name="username" value="{{ camera.username or '' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock"></i> Password
                                </label>
                                <input type="password" class="form-control" id="password" name="password" value="{{ camera.password or '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row" id="network_fields">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="ip_address" class="form-label">
                                    <i class="fas fa-network-wired"></i> IP Address
                                </label>
                                <input type="text" class="form-control" id="ip_address" name="ip_address" value="{{ camera.ip_address or '' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="port" class="form-label">
                                    <i class="fas fa-plug"></i> Port
                                </label>
                                <input type="number" class="form-control" id="port" name="port" value="{{ camera.port or '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6><i class="fas fa-map-marker-alt"></i> Location Information</h6>
                    
                    <div class="mb-3">
                        <label for="location_name" class="form-label">
                            <i class="fas fa-map-pin"></i> Location Name
                        </label>
                        <input type="text" class="form-control" id="location_name" name="location_name" value="{{ camera.location_name or '' }}">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">
                                    <i class="fas fa-globe"></i> Latitude
                                </label>
                                <input type="number" step="any" class="form-control" id="latitude" name="latitude" value="{{ camera.latitude or '' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">
                                    <i class="fas fa-globe"></i> Longitude
                                </label>
                                <input type="number" step="any" class="form-control" id="longitude" name="longitude" value="{{ camera.longitude or '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6><i class="fas fa-cogs"></i> Camera Settings</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="motion_detection" name="motion_detection" {{ 'checked' if camera.motion_detection else '' }}>
                                <label class="form-check-label" for="motion_detection">
                                    <i class="fas fa-running"></i> Enable Motion Detection
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_recording" name="is_recording" {{ 'checked' if camera.is_recording else '' }}>
                                <label class="form-check-label" for="is_recording">
                                    <i class="fas fa-record-vinyl"></i> Enable Recording
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('cameras') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="button" class="btn btn-info" onclick="testConnection()">
                            <i class="fas fa-plug"></i> Test Connection
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Camera
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-eye"></i> Live Preview
                </h5>
            </div>
            <div class="card-body text-center">
                {% if camera.status.value == 'active' %}
                <img src="{{ url_for('video_feed', camera_id=camera.id) }}" 
                     alt="{{ camera.name }}" 
                     class="img-fluid"
                     style="max-height: 300px;">
                {% else %}
                <div class="text-muted py-5">
                    <i class="fas fa-video-slash fa-3x mb-3"></i>
                    <p>Camera is offline</p>
                    <small>Start the camera to see live preview</small>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Camera Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <strong>Status:</strong>
                    </div>
                    <div class="col-6">
                        <span class="badge bg-{{ 'success' if camera.status.value == 'active' else 'danger' if camera.status.value == 'error' else 'secondary' }}">
                            {{ camera.status.value.title() }}
                        </span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <strong>Last Seen:</strong>
                    </div>
                    <div class="col-6">
                        <small>
                            {% if camera.last_seen %}
                                {{ camera.last_seen.strftime('%Y-%m-%d %H:%M:%S') }}
                            {% else %}
                                Never
                            {% endif %}
                        </small>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <strong>Created:</strong>
                    </div>
                    <div class="col-6">
                        <small>{{ camera.created_at.strftime('%Y-%m-%d') }}</small>
                    </div>
                </div>
                {% if camera.error_message %}
                <div class="row mt-2">
                    <div class="col-12">
                        <strong>Error:</strong>
                        <small class="text-danger">{{ camera.error_message }}</small>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Use the same JavaScript from add_camera.html
const connectionExamples = {
    'rtsp': {
        example: 'rtsp://username:password@*************:554/stream1',
        help: 'RTSP URL format: rtsp://[username:password@]host[:port]/path',
        showAuth: true,
        showNetwork: true
    },
    'usb': {
        example: '0',
        help: 'USB device number (usually 0 for first camera, 1 for second, etc.)',
        showAuth: false,
        showNetwork: false
    },
    'dahua': {
        example: 'rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0',
        help: 'Dahua RTSP format with channel and subtype parameters',
        showAuth: true,
        showNetwork: true
    },
    'hikvision': {
        example: 'rtsp://admin:password@*************:554/Streaming/Channels/101',
        help: 'Hikvision RTSP format with channel number',
        showAuth: true,
        showNetwork: true
    },
    'onvif': {
        example: 'onvif://*************:80',
        help: 'ONVIF camera address (will auto-discover stream URL)',
        showAuth: true,
        showNetwork: true
    },
    'http': {
        example: 'http://*************:8080/video',
        help: 'HTTP/MJPEG stream URL',
        showAuth: false,
        showNetwork: true
    }
};

function updateConnectionFields() {
    const cameraType = document.getElementById('camera_type').value;
    const connectionHelp = document.getElementById('connection_help');
    const authFields = document.getElementById('auth_fields');
    const networkFields = document.getElementById('network_fields');
    
    if (cameraType && connectionExamples[cameraType]) {
        const config = connectionExamples[cameraType];
        
        connectionHelp.textContent = config.help;
        authFields.style.display = config.showAuth ? 'block' : 'none';
        networkFields.style.display = config.showNetwork ? 'block' : 'none';
    }
}

function testConnection() {
    const testBtn = event.target;
    const originalText = testBtn.innerHTML;
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    testBtn.disabled = true;
    
    setTimeout(() => {
        testBtn.innerHTML = originalText;
        testBtn.disabled = false;
        alert('Connection test completed. Check the logs for details.');
    }, 2000);
}

document.addEventListener('DOMContentLoaded', function() {
    updateConnectionFields();
});
</script>
{% endblock %}
