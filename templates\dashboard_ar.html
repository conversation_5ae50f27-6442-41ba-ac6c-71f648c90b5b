{% extends "base_ar.html" %}

{% block title %}لوحة التحكم - نظام المراقبة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="arabic-numbers">{{ total_cameras }}</h4>
                        <p class="mb-0">إجمالي الكاميرات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-camera fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="arabic-numbers">{{ active_cameras }}</h4>
                        <p class="mb-0">الكاميرات النشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-play-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="arabic-numbers">{{ total_cameras - active_cameras }}</h4>
                        <p class="mb-0">الكاميرات غير المتصلة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-pause-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="recording-count" class="arabic-numbers">0</h4>
                        <p class="mb-0">التسجيل</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-record-vinyl fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Camera Grid -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-video"></i> البث المباشر للكاميرات
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setGridSize(1)">1×1</button>
                    <button type="button" class="btn btn-outline-primary btn-sm active" onclick="setGridSize(2)">2×2</button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setGridSize(3)">3×3</button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setGridSize(4)">4×4</button>
                </div>
            </div>
            <div class="card-body">
                <div id="camera-grid" class="camera-grid grid-2x2">
                    {% for camera in cameras %}
                    <div class="camera-item" data-camera-id="{{ camera.id }}">
                        <div class="camera-header">
                            <h6 class="camera-title">{{ camera.name }}</h6>
                            <div class="camera-controls">
                                <span class="badge bg-{{ 'success' if camera.status.value == 'active' else 'secondary' }}">
                                    {% if camera.status.value == 'active' %}
                                        نشط
                                    {% elif camera.status.value == 'error' %}
                                        خطأ
                                    {% elif camera.status.value == 'connecting' %}
                                        جاري الاتصال
                                    {% else %}
                                        غير نشط
                                    {% endif %}
                                </span>
                                <div class="btn-group btn-group-sm">
                                    {% if camera.status.value != 'active' %}
                                    <button class="btn btn-success btn-sm" onclick="startCamera({{ camera.id }})" title="تشغيل الكاميرا">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    {% else %}
                                    <button class="btn btn-danger btn-sm" onclick="stopCamera({{ camera.id }})" title="إيقاف الكاميرا">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-info btn-sm" onclick="takeSnapshot({{ camera.id }})" title="التقاط صورة">
                                        <i class="fas fa-camera"></i>
                                    </button>
                                    <button class="btn btn-primary btn-sm" onclick="openFullscreen({{ camera.id }})" title="ملء الشاشة">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="camera-video">
                            {% if camera.status.value == 'active' %}
                            <img src="{{ url_for('video_feed', camera_id=camera.id) }}" 
                                 alt="{{ camera.name }}" 
                                 class="camera-stream"
                                 onerror="this.src='{{ url_for('static', filename='img/camera-offline.png') }}'">
                            {% else %}
                            <div class="camera-offline">
                                <i class="fas fa-video-slash fa-3x"></i>
                                <p>الكاميرا غير متصلة</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="camera-info">
                            <small class="text-muted">
                                {{ camera.location_name or 'لا يوجد موقع' }} | 
                                {% if camera.camera_type.value == 'rtsp' %}
                                    RTSP
                                {% elif camera.camera_type.value == 'usb' %}
                                    USB
                                {% elif camera.camera_type.value == 'dahua' %}
                                    داهوا
                                {% elif camera.camera_type.value == 'hikvision' %}
                                    هيكفيجن
                                {% elif camera.camera_type.value == 'onvif' %}
                                    ONVIF
                                {% elif camera.camera_type.value == 'http' %}
                                    HTTP
                                {% else %}
                                    {{ camera.camera_type.value.upper() }}
                                {% endif %}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if cameras|length == 0 %}
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لم يتم تكوين أي كاميرات</h5>
                        <p class="text-muted">أضف أول كاميرا لبدء المراقبة</p>
                        {% if current_user.has_permission('manage') %}
                        <a href="{{ url_for('add_camera') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة كاميرا
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if current_user.has_permission('manage') %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('add_camera') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus fa-2x d-block mb-2"></i>
                            إضافة كاميرا جديدة
                        </a>
                    </div>
                    {% endif %}
                    
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('cameras') }}" class="btn btn-info w-100">
                            <i class="fas fa-list fa-2x d-block mb-2"></i>
                            إدارة الكاميرات
                        </a>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('map_view') }}" class="btn btn-success w-100">
                            <i class="fas fa-map-marker-alt fa-2x d-block mb-2"></i>
                            عرض الخريطة
                        </a>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-warning w-100" onclick="refreshAllCameras()">
                            <i class="fas fa-sync-alt fa-2x d-block mb-2"></i>
                            تحديث جميع الكاميرات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> إحصائيات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h3 class="text-success arabic-numbers">{{ active_cameras }}</h3>
                        <p class="text-muted">كاميرات متصلة</p>
                    </div>
                    <div class="col-6">
                        <h3 class="text-danger arabic-numbers">{{ total_cameras - active_cameras }}</h3>
                        <p class="text-muted">كاميرات غير متصلة</p>
                    </div>
                </div>
                <div class="progress mt-3">
                    {% set percentage = (active_cameras / total_cameras * 100) if total_cameras > 0 else 0 %}
                    <div class="progress-bar bg-success" style="width: {{ percentage }}%"></div>
                </div>
                <small class="text-muted">معدل الاتصال: {{ "%.1f"|format(percentage) }}%</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock"></i> آخر الأنشطة
                </h5>
            </div>
            <div class="card-body">
                <div class="activity-list">
                    {% for camera in cameras[:5] %}
                    <div class="d-flex align-items-center mb-2">
                        <div class="status-indicator status-{{ camera.status.value }} me-2"></div>
                        <div class="flex-grow-1">
                            <small class="fw-bold">{{ camera.name }}</small>
                            <br>
                            <small class="text-muted">
                                {% if camera.last_seen %}
                                    آخر ظهور: {{ camera.last_seen.strftime('%H:%M') }}
                                {% else %}
                                    لم يتم الاتصال مطلقاً
                                {% endif %}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fullscreen Modal -->
<div class="modal fade" id="fullscreenModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fullscreenModalLabel">عرض الكاميرا</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <img id="fullscreenImage" src="" alt="تغذية الكاميرا" class="w-100 h-100" style="object-fit: contain;">
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Camera control functions
function startCamera(cameraId) {
    fetch(`/cameras/${cameraId}/start`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم تشغيل الكاميرا بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('فشل في تشغيل الكاميرا: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('فشل في تشغيل الكاميرا', 'danger');
    });
}

function stopCamera(cameraId) {
    fetch(`/cameras/${cameraId}/stop`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إيقاف الكاميرا بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('فشل في إيقاف الكاميرا: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('فشل في إيقاف الكاميرا', 'danger');
    });
}

function takeSnapshot(cameraId) {
    fetch(`/api/cameras/${cameraId}/snapshot`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم التقاط الصورة بنجاح', 'success');
        } else {
            showNotification('فشل في التقاط الصورة: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('فشل في التقاط الصورة', 'danger');
    });
}

function openFullscreen(cameraId) {
    const img = document.getElementById('fullscreenImage');
    img.src = `/video_feed/${cameraId}`;
    const modal = new bootstrap.Modal(document.getElementById('fullscreenModal'));
    modal.show();
}

function setGridSize(size) {
    const grid = document.getElementById('camera-grid');
    grid.className = `camera-grid grid-${size}x${size}`;
    
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
}

function refreshAllCameras() {
    showNotification('جاري تحديث جميع الكاميرات...', 'info');
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Update recording count
function updateRecordingCount() {
    const recordingCameras = document.querySelectorAll('[data-recording="true"]').length;
    document.getElementById('recording-count').textContent = recordingCameras;
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateRecordingCount();
    formatArabicNumbers();
    
    // Auto-refresh camera status every 30 seconds
    setInterval(() => {
        location.reload();
    }, 30000);
});
</script>
{% endblock %}
