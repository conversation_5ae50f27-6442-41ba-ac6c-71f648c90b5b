from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash
from models import User, UserRole, LoginHistory, db
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = bool(request.form.get('remember'))
        
        if not username or not password:
            flash('Please enter both username and password', 'error')
            return render_template('auth/login.html')
        
        user = User.query.filter_by(username=username).first()
        
        # Log login attempt
        login_history = LoginHistory(
            user_id=user.id if user else None,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            success=False
        )
        
        if user and user.is_active and user.check_password(password):
            # Reset login attempts on successful login
            user.login_attempts = 0
            user.last_login = datetime.utcnow()
            login_history.success = True
            
            db.session.add(login_history)
            db.session.commit()
            
            login_user(user, remember=remember)
            
            # Redirect to next page or dashboard
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('main.dashboard'))
        else:
            # Handle failed login
            if user:
                user.login_attempts += 1
                if user.login_attempts >= 5:
                    user.is_active = False
                    flash('Account locked due to too many failed login attempts. Contact administrator.', 'error')
                else:
                    flash(f'Invalid credentials. {5 - user.login_attempts} attempts remaining.', 'error')
            else:
                flash('Invalid credentials', 'error')
            
            db.session.add(login_history)
            db.session.commit()
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out successfully', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    # Only allow registration if no users exist (first user) or current user is admin
    user_count = User.query.count()
    if user_count > 0 and (not current_user.is_authenticated or not current_user.has_permission('admin')):
        flash('Registration is not allowed', 'error')
        return redirect(url_for('auth.login'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        role = request.form.get('role', 'viewer')
        
        # Validation
        if not all([username, email, password, confirm_password]):
            flash('All fields are required', 'error')
            return render_template('auth/register.html')
        
        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('auth/register.html')
        
        if len(password) < 6:
            flash('Password must be at least 6 characters long', 'error')
            return render_template('auth/register.html')
        
        # Check if user already exists
        if User.query.filter_by(username=username).first():
            flash('Username already exists', 'error')
            return render_template('auth/register.html')
        
        if User.query.filter_by(email=email).first():
            flash('Email already exists', 'error')
            return render_template('auth/register.html')
        
        # Create new user
        try:
            # First user becomes admin
            if user_count == 0:
                role = 'admin'
            
            user = User(
                username=username,
                email=email,
                role=UserRole(role)
            )
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            flash('Registration successful! You can now log in.', 'success')
            return redirect(url_for('auth.login'))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Registration error: {str(e)}")
            flash('Registration failed. Please try again.', 'error')
    
    return render_template('auth/register.html')

@auth_bp.route('/profile')
@login_required
def profile():
    return render_template('auth/profile.html', user=current_user)

@auth_bp.route('/change_password', methods=['POST'])
@login_required
def change_password():
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')
    
    if not all([current_password, new_password, confirm_password]):
        flash('All fields are required', 'error')
        return redirect(url_for('auth.profile'))
    
    if not current_user.check_password(current_password):
        flash('Current password is incorrect', 'error')
        return redirect(url_for('auth.profile'))
    
    if new_password != confirm_password:
        flash('New passwords do not match', 'error')
        return redirect(url_for('auth.profile'))
    
    if len(new_password) < 6:
        flash('Password must be at least 6 characters long', 'error')
        return redirect(url_for('auth.profile'))
    
    try:
        current_user.set_password(new_password)
        db.session.commit()
        flash('Password changed successfully', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"Password change error: {str(e)}")
        flash('Failed to change password', 'error')
    
    return redirect(url_for('auth.profile'))

@auth_bp.route('/users')
@login_required
def users():
    if not current_user.has_permission('admin'):
        flash('Access denied', 'error')
        return redirect(url_for('main.dashboard'))
    
    users = User.query.all()
    return render_template('auth/users.html', users=users)

@auth_bp.route('/users/<int:user_id>/toggle_status', methods=['POST'])
@login_required
def toggle_user_status(user_id):
    if not current_user.has_permission('admin'):
        return jsonify({'success': False, 'message': 'Access denied'}), 403
    
    user = User.query.get_or_404(user_id)
    
    # Prevent admin from deactivating themselves
    if user.id == current_user.id:
        return jsonify({'success': False, 'message': 'Cannot deactivate your own account'}), 400
    
    try:
        user.is_active = not user.is_active
        user.login_attempts = 0  # Reset login attempts when reactivating
        db.session.commit()
        
        status = 'activated' if user.is_active else 'deactivated'
        return jsonify({'success': True, 'message': f'User {status} successfully'})
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error toggling user status: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to update user status'}), 500

@auth_bp.route('/users/<int:user_id>/change_role', methods=['POST'])
@login_required
def change_user_role(user_id):
    if not current_user.has_permission('admin'):
        return jsonify({'success': False, 'message': 'Access denied'}), 403
    
    user = User.query.get_or_404(user_id)
    new_role = request.json.get('role')
    
    if new_role not in [role.value for role in UserRole]:
        return jsonify({'success': False, 'message': 'Invalid role'}), 400
    
    # Prevent admin from changing their own role
    if user.id == current_user.id:
        return jsonify({'success': False, 'message': 'Cannot change your own role'}), 400
    
    try:
        user.role = UserRole(new_role)
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'User role updated successfully'})
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error changing user role: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to update user role'}), 500

def require_permission(permission):
    """Decorator to require specific permission"""
    def decorator(f):
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('auth.login'))
            if not current_user.has_permission(permission):
                flash('Access denied', 'error')
                return redirect(url_for('main.dashboard'))
            return f(*args, **kwargs)
        decorated_function.__name__ = f.__name__
        return decorated_function
    return decorator
