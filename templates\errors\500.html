{% extends "base.html" %}

{% block title %}Server Error - Surveillance System{% endblock %}

{% block content %}
<div class="container-fluid d-flex align-items-center justify-content-center" style="min-height: 60vh;">
    <div class="text-center">
        <div class="error-icon mb-4">
            <i class="fas fa-exclamation-triangle fa-5x text-warning"></i>
        </div>
        
        <h1 class="display-1 fw-bold text-danger">500</h1>
        <h2 class="mb-4">Internal Server Error</h2>
        
        <p class="lead text-muted mb-4">
            Something went wrong on our end. We're working to fix it.
        </p>
        
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-info-circle"></i>
            <strong>What happened?</strong> The server encountered an unexpected error and couldn't complete your request.
        </div>
        
        <div class="d-flex justify-content-center gap-3 mb-4">
            <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                <i class="fas fa-home"></i> Go to Dashboard
            </a>
            <button onclick="location.reload()" class="btn btn-outline-primary">
                <i class="fas fa-redo"></i> Try Again
            </button>
            <button onclick="history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Go Back
            </button>
        </div>
        
        <div class="card mx-auto" style="max-width: 500px;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools"></i> Troubleshooting Tips
                </h5>
            </div>
            <div class="card-body text-start">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success"></i>
                        Check if all cameras are properly connected
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success"></i>
                        Verify network connectivity
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success"></i>
                        Ensure sufficient disk space
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success"></i>
                        Try refreshing the page
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                If the problem persists, please contact your system administrator.
            </small>
        </div>
    </div>
</div>

<style>
.error-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}
</style>
{% endblock %}
