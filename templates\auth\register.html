{% extends "base.html" %}

{% block title %}Register User - Surveillance System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus"></i> Register New User
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user"></i> Username *
                        </label>
                        <input type="text" class="form-control" id="username" name="username" required>
                        <div class="form-text">Choose a unique username</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i> Email Address *
                        </label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i> Password *
                        </label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="form-text">Minimum 6 characters</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">
                            <i class="fas fa-lock"></i> Confirm Password *
                        </label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    {% if current_user.is_authenticated and current_user.has_permission('admin') %}
                    <div class="mb-3">
                        <label for="role" class="form-label">
                            <i class="fas fa-user-tag"></i> User Role
                        </label>
                        <select class="form-select" id="role" name="role">
                            <option value="viewer">Viewer - View cameras only</option>
                            <option value="manager">Manager - Manage cameras and view</option>
                            <option value="admin">Admin - Full system access</option>
                        </select>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between">
                        {% if current_user.is_authenticated %}
                        <a href="{{ url_for('auth.users') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Users
                        </a>
                        {% else %}
                        <a href="{{ url_for('auth.login') }}" class="btn btn-secondary">
                            <i class="fas fa-sign-in-alt"></i> Login Instead
                        </a>
                        {% endif %}
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Register User
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> User Roles Explained
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <div class="mb-2">
                            <span class="badge bg-info me-2">Viewer</span>
                            Can view camera feeds and dashboard only
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-warning me-2">Manager</span>
                            Can add, edit, and manage cameras + viewer permissions
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-danger me-2">Admin</span>
                            Full system access including user management
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('input', validatePassword);
    confirmPassword.addEventListener('input', validatePassword);
});
</script>
{% endblock %}
