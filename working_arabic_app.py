#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المراقبة العربي - نسخة تعمل بشكل مؤكد
Arabic Surveillance System - Working Version
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session, Response
import json
import os
import time
import random
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'arabic-surveillance-system-2024'

# بيانات النظام
users = {'admin': 'admin123'}
cameras_data = [
    {
        'id': 1, 
        'name': 'كاميرا المدخل الرئيسي', 
        'type': 'داهوا', 
        'status': 'نشط', 
        'location': 'المدخل الرئيسي',
        'channel': 1,
        'stream_quality': 'main',
        'ip_address': '*************',
        'created': '2024-01-15'
    },
    {
        'id': 2, 
        'name': 'كاميرا موقف السيارات', 
        'type': 'هيكفيجن', 
        'status': 'نشط', 
        'location': 'موقف السيارات',
        'channel': 2,
        'stream_quality': 'sub',
        'ip_address': '*************',
        'created': '2024-01-16'
    },
    {
        'id': 3, 
        'name': 'كاميرا الحديقة الخلفية', 
        'type': 'RTSP', 
        'status': 'نشط', 
        'location': 'الحديقة الخلفية',
        'channel': 1,
        'stream_quality': 'main',
        'ip_address': '*************',
        'created': '2024-01-17'
    }
]

# قالب تسجيل الدخول
LOGIN_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المراقبة العربي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 450px;
            width: 100%;
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        .form-control { text-align: right; border-radius: 10px; padding: 12px 15px; }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div style="font-size: 4rem; margin-bottom: 1rem;">🎥</div>
            <h2 class="mb-3">نظام المراقبة العربي</h2>
            <p class="mb-0">يرجى تسجيل الدخول للمتابعة</p>
        </div>
        <div style="padding: 2rem;">
            {% if error %}
                <div class="alert alert-danger text-center">{{ error }}</div>
            {% endif %}
            
            <form method="POST">
                <div class="mb-4">
                    <label class="form-label fw-bold">اسم المستخدم</label>
                    <input type="text" class="form-control" name="username" placeholder="أدخل اسم المستخدم" required>
                </div>
                <div class="mb-4">
                    <label class="form-label fw-bold">كلمة المرور</label>
                    <input type="password" class="form-control" name="password" placeholder="أدخل كلمة المرور" required>
                </div>
                <button type="submit" class="btn btn-primary btn-login w-100 mb-3">تسجيل الدخول</button>
            </form>
            
            <div class="alert alert-info text-center">
                <strong>🔑 بيانات الدخول:</strong><br>
                <code>admin</code> / <code>admin123</code>
            </div>
        </div>
    </div>
</body>
</html>
"""

# قالب لوحة التحكم
DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام المراقبة العربي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border: none; border-radius: 15px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08); }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0 !important; }
        .camera-card { transition: all 0.3s ease; }
        .camera-card:hover { transform: translateY(-5px); }
        .camera-preview {
            height: 200px;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            border-radius: 15px 15px 0 0;
        }
        .camera-stream { width: 100%; height: 100%; object-fit: cover; border-radius: 15px 15px 0 0; }
        .camera-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="{{ url_for('dashboard') }}">
                <i class="fas fa-video me-2"></i>نظام المراقبة العربي
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="{{ url_for('add_camera') }}">
                    <i class="fas fa-plus me-1"></i>إضافة كاميرا
                </a>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="text-center mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; padding: 2rem;">
            <h1 class="display-4 mb-3">🎥 نظام المراقبة العربي</h1>
            <p class="lead">مراقبة متطورة مع دعم كامل للغة العربية</p>
        </div>

        <div class="row mb-4">
            <div class="col-md-4 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-camera fa-3x mb-3"></i>
                        <h2>{{ total_cameras }}</h2>
                        <p class="mb-0">إجمالي الكاميرات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-play-circle fa-3x mb-3"></i>
                        <h2>{{ active_cameras }}</h2>
                        <p class="mb-0">الكاميرات النشطة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt fa-3x mb-3"></i>
                        <h2>100%</h2>
                        <p class="mb-0">أمان النظام</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-video me-2"></i>الكاميرات المتاحة
                        </h4>
                    </div>
                    <div class="card-body">
                        {% if cameras %}
                            <div class="row">
                                {% for camera in cameras %}
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="camera-card card">
                                        <div class="camera-preview">
                                            <span class="camera-status">{{ camera.status }}</span>
                                            <img src="{{ url_for('video_feed', camera_id=camera.id) }}" 
                                                 alt="{{ camera.name }}" 
                                                 class="camera-stream"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; flex-direction: column; color: white;">
                                                <i class="fas fa-video fa-3x mb-2"></i>
                                                <small>{{ camera.name }}</small>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <h5 class="card-title">{{ camera.name }}</h5>
                                            <p class="card-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-map-marker-alt me-1"></i>{{ camera.location }}<br>
                                                    <i class="fas fa-tv me-1"></i>{{ camera.type }} - القناة {{ camera.channel }}
                                                </small>
                                            </p>
                                            <div class="btn-group w-100">
                                                <button class="btn btn-primary btn-sm" onclick="viewCamera({{ camera.id }})">
                                                    <i class="fas fa-eye"></i> عرض
                                                </button>
                                                <button class="btn btn-info btn-sm" onclick="takeSnapshot({{ camera.id }})">
                                                    <i class="fas fa-camera"></i> صورة
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-camera fa-5x text-muted mb-4"></i>
                                <h3 class="text-muted">لا توجد كاميرات</h3>
                                <p class="text-muted">أضف أول كاميرا لبدء المراقبة</p>
                                <a href="{{ url_for('add_camera') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus me-2"></i>إضافة كاميرا جديدة
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewCamera(id) {
            window.open('/camera/' + id, '_blank');
        }

        function takeSnapshot(id) {
            alert('تم التقاط صورة من الكاميرا رقم ' + id);
        }
    </script>
</body>
</html>
"""

# قالب إضافة كاميرا
ADD_CAMERA_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة كاميرا جديدة - نظام المراقبة العربي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border: none; border-radius: 15px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08); }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0 !important; }
        .form-control, .form-select { text-align: right; border-radius: 10px; padding: 12px 15px; }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 10px; }
        .connection-example { background: #e3f2fd; border-left: 4px solid #2196f3; padding: 10px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="{{ url_for('dashboard') }}">
                <i class="fas fa-video me-2"></i>نظام المراقبة العربي
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link active" href="{{ url_for('add_camera') }}">
                    <i class="fas fa-plus me-1"></i>إضافة كاميرا
                </a>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-plus-circle me-2"></i>إضافة كاميرا جديدة
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>تكوين الكاميرا
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">اسم الكاميرا *</label>
                                        <input type="text" class="form-control" name="name" placeholder="مثال: كاميرا المدخل الرئيسي" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">نوع الكاميرا *</label>
                                        <select class="form-select" name="camera_type" required onchange="updateExample()">
                                            <option value="">اختر نوع الكاميرا</option>
                                            <option value="rtsp">RTSP - كاميرات الشبكة العامة</option>
                                            <option value="usb">USB - كاميرات الويب المحلية</option>
                                            <option value="داهوا">داهوا - كاميرات داهوا IP</option>
                                            <option value="هيكفيجن">هيكفيجن - كاميرات هيكفيجن IP</option>
                                            <option value="onvif">ONVIF - كاميرات متوافقة مع ONVIF</option>
                                            <option value="http">HTTP - تدفقات HTTP/MJPEG</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">رقم القناة *</label>
                                        <select class="form-select" name="channel" required>
                                            <option value="">اختر رقم القناة</option>
                                            <option value="1">القناة 1</option>
                                            <option value="2">القناة 2</option>
                                            <option value="3">القناة 3</option>
                                            <option value="4">القناة 4</option>
                                            <option value="5">القناة 5</option>
                                            <option value="6">القناة 6</option>
                                            <option value="7">القناة 7</option>
                                            <option value="8">القناة 8</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">جودة التدفق</label>
                                        <select class="form-select" name="stream_quality">
                                            <option value="main">التدفق الرئيسي (جودة عالية)</option>
                                            <option value="sub" selected>التدفق الفرعي (جودة متوسطة)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">اسم المستخدم</label>
                                        <input type="text" class="form-control" name="username" value="admin">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">كلمة المرور</label>
                                        <input type="password" class="form-control" name="password" value="password">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">عنوان IP *</label>
                                        <input type="text" class="form-control" name="ip_address" placeholder="*************" value="*************">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">المنفذ</label>
                                        <input type="number" class="form-control" name="port" placeholder="554" value="554">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">اسم الموقع</label>
                                <input type="text" class="form-control" name="location_name" placeholder="مثال: المدخل الرئيسي، موقف السيارات">
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right me-2"></i>العودة
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>إضافة الكاميرا
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>أمثلة الاتصال
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="connectionExamples">
                            <div class="connection-example">
                                <h6>RTSP عام:</h6>
                                <small>rtsp://admin:password@*************:554/stream1</small>
                            </div>

                            <div class="connection-example">
                                <h6>USB:</h6>
                                <small>0 (رقم الجهاز)</small>
                            </div>

                            <div class="connection-example">
                                <h6>داهوا:</h6>
                                <small>rtsp://admin:pass@ip:554/cam/realmonitor?channel=1&subtype=0</small>
                            </div>

                            <div class="connection-example">
                                <h6>هيكفيجن:</h6>
                                <small>rtsp://admin:pass@ip:554/Streaming/Channels/101</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>نصائح
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                تأكد من اتصال الكاميرا بالشبكة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                استخدم أسماء وصفية للكاميرات
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                اختبر الاتصال في VLC أولاً
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateExample() {
            // يمكن إضافة تحديث الأمثلة هنا
        }
    </script>
</body>
</html>
"""

# قالب عرض الكاميرا
CAMERA_VIEW_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ camera.name }} - نظام المراقبة العربي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background-color: #000;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .camera-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .camera-stream {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .camera-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .camera-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 1000;
        }
        .btn-control {
            background: rgba(0, 0, 0, 0.8);
            border: none;
            color: white;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-control:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="camera-container">
        <div class="camera-controls">
            <button class="btn-control" onclick="goBack()">
                ← العودة
            </button>
            <button class="btn-control" onclick="takeSnapshot()">
                📷 صورة
            </button>
            <button class="btn-control" onclick="refreshStream()">
                🔄 تحديث
            </button>
        </div>
        
        <img src="{{ url_for('video_feed', camera_id=camera.id) }}" 
             alt="{{ camera.name }}" 
             class="camera-stream"
             id="cameraStream">
        
        <div class="camera-info">
            <h5>🎥 {{ camera.name }}</h5>
            <p><strong>النوع:</strong> {{ camera.type }}</p>
            <p><strong>الموقع:</strong> {{ camera.location }}</p>
            <p><strong>القناة:</strong> {{ camera.channel }}</p>
            <p><strong>الحالة:</strong> {{ camera.status }}</p>
        </div>
    </div>
    
    <script>
        function goBack() {
            window.close();
        }
        
        function takeSnapshot() {
            alert('تم التقاط صورة من {{ camera.name }}');
        }
        
        function refreshStream() {
            const stream = document.getElementById('cameraStream');
            const src = stream.src;
            stream.src = '';
            setTimeout(() => {
                stream.src = src + '?t=' + Date.now();
            }, 100);
        }
    </script>
</body>
</html>
"""

# الصفحات
@app.route('/')
def index():
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    error = None
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if username in users and users[username] == password:
            session['logged_in'] = True
            session['username'] = username
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            error = 'اسم المستخدم أو كلمة المرور غير صحيحة'
    
    return render_template_string(LOGIN_TEMPLATE, error=error)

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'logged_in' not in session:
        return redirect(url_for('login'))
    
    total_cameras = len(cameras_data)
    active_cameras = len([c for c in cameras_data if c['status'] == 'نشط'])
    
    return render_template_string(DASHBOARD_TEMPLATE, 
                                cameras=cameras_data,
                                total_cameras=total_cameras,
                                active_cameras=active_cameras)

@app.route('/video_feed/<int:camera_id>')
def video_feed(camera_id):
    """محاكي تدفق الكاميرا"""
    def generate():
        while True:
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
            
            # إنشاء محتوى SVG
            svg_content = f'''<svg width="640" height="480" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="640" height="480" fill="url(#grad1)" />
                <text x="320" y="200" font-family="Arial" font-size="24" fill="white" text-anchor="middle" font-weight="bold">
                    🎥 كاميرا رقم {camera_id}
                </text>
                <text x="320" y="240" font-family="Arial" font-size="16" fill="white" text-anchor="middle">
                    {timestamp}
                </text>
                <circle cx="{320 + random.randint(-50, 50)}" cy="{300 + random.randint(-30, 30)}" r="10" fill="red" opacity="0.8"/>
                <text x="320" y="400" font-family="Arial" font-size="14" fill="white" text-anchor="middle">
                    🔴 البث المباشر - نظام المراقبة العربي
                </text>
            </svg>'''
            
            yield (b'--frame\r\n'
                   b'Content-Type: image/svg+xml\r\n\r\n' + 
                   svg_content.encode('utf-8') + b'\r\n')
            
            time.sleep(1)
    
    return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/camera/<int:camera_id>')
def view_camera(camera_id):
    if 'logged_in' not in session:
        return redirect(url_for('login'))
    
    camera = None
    for cam in cameras_data:
        if cam['id'] == camera_id:
            camera = cam
            break
    
    if not camera:
        flash('الكاميرا غير موجودة', 'error')
        return redirect(url_for('dashboard'))
    
    return render_template_string(CAMERA_VIEW_TEMPLATE, camera=camera)

@app.route('/add_camera', methods=['GET', 'POST'])
def add_camera():
    if 'logged_in' not in session:
        return redirect(url_for('login'))

    if request.method == 'POST':
        try:
            # استخراج البيانات من النموذج
            name = request.form.get('name', '').strip()
            camera_type = request.form.get('camera_type', '').strip()
            channel = request.form.get('channel', '').strip()
            stream_quality = request.form.get('stream_quality', 'sub').strip()
            ip_address = request.form.get('ip_address', '').strip()
            port = request.form.get('port', '554').strip()
            username = request.form.get('username', 'admin').strip()
            password = request.form.get('password', 'password').strip()
            location_name = request.form.get('location_name', '').strip()

            # التحقق من البيانات المطلوبة
            if not name:
                flash('يرجى إدخال اسم الكاميرا', 'error')
                return render_template_string(ADD_CAMERA_TEMPLATE)

            if not camera_type:
                flash('يرجى اختيار نوع الكاميرا', 'error')
                return render_template_string(ADD_CAMERA_TEMPLATE)

            if not channel:
                flash('يرجى اختيار رقم القناة', 'error')
                return render_template_string(ADD_CAMERA_TEMPLATE)

            if camera_type != 'usb' and not ip_address:
                flash('يرجى إدخال عنوان IP للكاميرا', 'error')
                return render_template_string(ADD_CAMERA_TEMPLATE)

            # إنشاء سلسلة الاتصال تلقائياً
            connection_string = generate_connection_string(
                camera_type, channel, stream_quality, ip_address, port, username, password
            )

            # إنشاء كاميرا جديدة
            new_camera = {
                'id': len(cameras_data) + 1,
                'name': name,
                'type': camera_type,
                'status': 'نشط',
                'location': location_name or 'غير محدد',
                'channel': int(channel),
                'stream_quality': stream_quality,
                'ip_address': ip_address,
                'port': int(port) if port.isdigit() else 554,
                'username': username,
                'password': password,
                'connection_string': connection_string,
                'created': datetime.now().strftime('%Y-%m-%d')
            }

            # إضافة الكاميرا للقائمة
            cameras_data.append(new_camera)

            flash(f'تم إضافة الكاميرا "{name}" بنجاح!', 'success')
            return redirect(url_for('dashboard'))

        except Exception as e:
            flash('حدث خطأ أثناء إضافة الكاميرا. يرجى المحاولة مرة أخرى.', 'error')
            return render_template_string(ADD_CAMERA_TEMPLATE)

    # عرض نموذج إضافة الكاميرا
    return render_template_string(ADD_CAMERA_TEMPLATE)

def generate_connection_string(camera_type, channel, stream_quality, ip_address, port, username, password):
    """إنشاء سلسلة الاتصال تلقائياً"""
    if camera_type == 'usb':
        return str(int(channel) - 1)  # USB يبدأ من 0
    elif camera_type == 'داهوا':
        subtype = '0' if stream_quality == 'main' else '1'
        return f'rtsp://{username}:{password}@{ip_address}:{port}/cam/realmonitor?channel={channel}&subtype={subtype}'
    elif camera_type == 'هيكفيجن':
        channel_id = f'{channel}01' if stream_quality == 'main' else f'{channel}02'
        return f'rtsp://{username}:{password}@{ip_address}:{port}/Streaming/Channels/{channel_id}'
    elif camera_type == 'rtsp':
        return f'rtsp://{username}:{password}@{ip_address}:{port}/stream{channel}'
    elif camera_type == 'onvif':
        return f'onvif://{ip_address}:80/onvif/device_service'
    elif camera_type == 'http':
        return f'http://{ip_address}:8080/video{channel}'
    else:
        return f'rtsp://{username}:{password}@{ip_address}:{port}/stream{channel}'

if __name__ == '__main__':
    print("🎥 نظام المراقبة العربي - نسخة تعمل")
    print("=" * 50)
    print("📍 الخادم: http://localhost:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("✅ عرض الكاميرات: يعمل")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
