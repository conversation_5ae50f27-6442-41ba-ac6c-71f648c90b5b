# -*- coding: utf-8 -*-
"""
Arabic Language Support for Surveillance System
نظام دعم اللغة العربية لنظام المراقبة
"""

# Arabic translations dictionary
TRANSLATIONS = {
    'ar': {
        # Navigation
        'surveillance_system': 'نظام المراقبة',
        'dashboard': 'لوحة التحكم',
        'cameras': 'الكاميرات',
        'map': 'الخريطة',
        'add_camera': 'إضافة كاميرا',
        'users': 'المستخدمين',
        'profile': 'الملف الشخصي',
        'logout': 'تسجيل الخروج',
        'login': 'تسجيل الدخول',
        
        # Dashboard
        'total_cameras': 'إجمالي الكاميرات',
        'active_cameras': 'الكاميرات النشطة',
        'offline_cameras': 'الكاميرات غير المتصلة',
        'recording': 'التسجيل',
        'live_camera_feeds': 'البث المباشر للكاميرات',
        'camera_offline': 'الكاميرا غير متصلة',
        'no_cameras_configured': 'لم يتم تكوين أي كاميرات',
        'add_first_camera': 'أضف أول كاميرا لبدء المراقبة',
        
        # Camera Management
        'camera_management': 'إدارة الكاميرات',
        'camera_list': 'قائمة الكاميرات',
        'name': 'الاسم',
        'type': 'النوع',
        'location': 'الموقع',
        'status': 'الحالة',
        'last_seen': 'آخر ظهور',
        'actions': 'الإجراءات',
        'active': 'نشط',
        'inactive': 'غير نشط',
        'error': 'خطأ',
        'connecting': 'جاري الاتصال',
        'motion_detection': 'كشف الحركة',
        'never': 'أبداً',
        
        # Camera Actions
        'start_camera': 'تشغيل الكاميرا',
        'stop_camera': 'إيقاف الكاميرا',
        'view_camera': 'عرض الكاميرا',
        'take_snapshot': 'التقاط صورة',
        'edit_camera': 'تعديل الكاميرا',
        'delete_camera': 'حذف الكاميرا',
        'camera_view': 'عرض الكاميرا',
        'fullscreen': 'ملء الشاشة',
        'close': 'إغلاق',
        
        # Add/Edit Camera
        'add_new_camera': 'إضافة كاميرا جديدة',
        'edit_camera_title': 'تعديل الكاميرا',
        'camera_configuration': 'تكوين الكاميرا',
        'camera_name': 'اسم الكاميرا',
        'camera_type': 'نوع الكاميرا',
        'description': 'الوصف',
        'connection_string': 'سلسلة الاتصال',
        'username': 'اسم المستخدم',
        'password': 'كلمة المرور',
        'ip_address': 'عنوان IP',
        'port': 'المنفذ',
        'location_information': 'معلومات الموقع',
        'location_name': 'اسم الموقع',
        'latitude': 'خط العرض',
        'longitude': 'خط الطول',
        'camera_settings': 'إعدادات الكاميرا',
        'enable_motion_detection': 'تفعيل كشف الحركة',
        'enable_recording': 'تفعيل التسجيل',
        'test_connection': 'اختبار الاتصال',
        'save': 'حفظ',
        'cancel': 'إلغاء',
        'update': 'تحديث',
        'back_to_cameras': 'العودة للكاميرات',
        
        # Connection Examples
        'connection_examples': 'أمثلة الاتصال',
        'help': 'المساعدة',
        'supported_camera_types': 'أنواع الكاميرات المدعومة',
        'network_cameras': 'كاميرات الشبكة مع بروتوكول RTSP',
        'local_webcams': 'كاميرات الويب المحلية USB',
        'dahua_cameras': 'كاميرات داهوا IP',
        'hikvision_cameras': 'كاميرات هيكفيجن IP',
        'onvif_cameras': 'كاميرات متوافقة مع ONVIF',
        'http_streams': 'تدفقات HTTP/MJPEG',
        
        # Map
        'camera_locations': 'مواقع الكاميرات',
        'interactive_map': 'الخريطة التفاعلية',
        'camera_locations_list': 'قائمة مواقع الكاميرات',
        'coordinates': 'الإحداثيات',
        'no_location': 'لا يوجد موقع',
        'no_coordinates': 'لا توجد إحداثيات',
        'locate': 'تحديد الموقع',
        'view': 'عرض',
        
        # Authentication
        'please_sign_in': 'يرجى تسجيل الدخول للمتابعة',
        'sign_in': 'تسجيل الدخول',
        'remember_me': 'تذكرني',
        'default_credentials': 'بيانات الدخول الافتراضية',
        'invalid_credentials': 'بيانات دخول غير صحيحة',
        'account_locked': 'تم قفل الحساب بسبب محاولات دخول فاشلة متعددة',
        'logged_out_successfully': 'تم تسجيل الخروج بنجاح',
        'access_denied': 'تم رفض الوصول',
        
        # User Management
        'user_management': 'إدارة المستخدمين',
        'add_user': 'إضافة مستخدم',
        'system_users': 'مستخدمي النظام',
        'user': 'المستخدم',
        'role': 'الدور',
        'email': 'البريد الإلكتروني',
        'created': 'تاريخ الإنشاء',
        'admin': 'مدير',
        'manager': 'مدير تنفيذي',
        'viewer': 'مشاهد',
        'administrators': 'المديرين',
        'managers': 'المديرين التنفيذيين',
        'viewers': 'المشاهدين',
        'active_users': 'المستخدمين النشطين',
        'current_user': 'المستخدم الحالي',
        
        # User Profile
        'user_profile': 'الملف الشخصي للمستخدم',
        'profile_information': 'معلومات الملف الشخصي',
        'account_status': 'حالة الحساب',
        'member_since': 'عضو منذ',
        'last_login': 'آخر تسجيل دخول',
        'change_password': 'تغيير كلمة المرور',
        'current_password': 'كلمة المرور الحالية',
        'new_password': 'كلمة المرور الجديدة',
        'confirm_password': 'تأكيد كلمة المرور',
        'security_tips': 'نصائح الأمان',
        
        # Register
        'register_new_user': 'تسجيل مستخدم جديد',
        'register_user': 'تسجيل المستخدم',
        'email_address': 'عنوان البريد الإلكتروني',
        'user_role': 'دور المستخدم',
        'user_roles_explained': 'شرح أدوار المستخدمين',
        'login_instead': 'تسجيل الدخول بدلاً من ذلك',
        'back_to_users': 'العودة للمستخدمين',
        
        # Role Descriptions
        'viewer_desc': 'يمكن عرض تغذيات الكاميرا ولوحة التحكم فقط',
        'manager_desc': 'يمكن إضافة وتعديل وإدارة الكاميرات + صلاحيات المشاهد',
        'admin_desc': 'وصول كامل للنظام بما في ذلك إدارة المستخدمين',
        
        # Messages
        'camera_added_successfully': 'تم إضافة الكاميرا بنجاح!',
        'camera_updated_successfully': 'تم تحديث الكاميرا بنجاح!',
        'camera_deleted_successfully': 'تم حذف الكاميرا بنجاح',
        'camera_started_successfully': 'تم تشغيل الكاميرا بنجاح',
        'camera_stopped_successfully': 'تم إيقاف الكاميرا بنجاح',
        'snapshot_taken_successfully': 'تم التقاط الصورة بنجاح',
        'failed_to_add_camera': 'فشل في إضافة الكاميرا',
        'failed_to_update_camera': 'فشل في تحديث الكاميرا',
        'failed_to_delete_camera': 'فشل في حذف الكاميرا',
        'failed_to_start_camera': 'فشل في تشغيل الكاميرا',
        'failed_to_stop_camera': 'فشل في إيقاف الكاميرا',
        'failed_to_take_snapshot': 'فشل في التقاط الصورة',
        'camera_not_available': 'الكاميرا غير متاحة',
        
        # Validation Messages
        'all_fields_required': 'جميع الحقول مطلوبة',
        'passwords_do_not_match': 'كلمات المرور غير متطابقة',
        'password_min_length': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
        'username_exists': 'اسم المستخدم موجود بالفعل',
        'email_exists': 'البريد الإلكتروني موجود بالفعل',
        'registration_successful': 'تم التسجيل بنجاح! يمكنك الآن تسجيل الدخول',
        'registration_failed': 'فشل التسجيل. يرجى المحاولة مرة أخرى',
        
        # Error Pages
        'page_not_found': 'الصفحة غير موجودة',
        'page_not_found_desc': 'الصفحة التي تبحث عنها غير موجودة أو تم نقلها',
        'go_to_dashboard': 'الذهاب للوحة التحكم',
        'go_back': 'العودة',
        'quick_links': 'روابط سريعة',
        'server_error': 'خطأ في الخادم',
        'server_error_desc': 'حدث خطأ غير متوقع. نحن نعمل على إصلاحه',
        'what_happened': 'ماذا حدث؟',
        'server_error_explanation': 'واجه الخادم خطأ غير متوقع ولم يتمكن من إكمال طلبك',
        'try_again': 'حاول مرة أخرى',
        'troubleshooting_tips': 'نصائح استكشاف الأخطاء',
        'check_cameras_connected': 'تحقق من اتصال جميع الكاميرات بشكل صحيح',
        'verify_network': 'تحقق من اتصال الشبكة',
        'ensure_disk_space': 'تأكد من وجود مساحة كافية على القرص',
        'try_refresh': 'حاول تحديث الصفحة',
        'contact_admin': 'إذا استمرت المشكلة، يرجى الاتصال بمدير النظام',
        
        # General
        'yes': 'نعم',
        'no': 'لا',
        'ok': 'موافق',
        'confirm': 'تأكيد',
        'delete': 'حذف',
        'edit': 'تعديل',
        'add': 'إضافة',
        'search': 'بحث',
        'filter': 'تصفية',
        'sort': 'ترتيب',
        'refresh': 'تحديث',
        'loading': 'جاري التحميل...',
        'please_wait': 'يرجى الانتظار...',
        'success': 'نجح',
        'warning': 'تحذير',
        'info': 'معلومات',
        'required': 'مطلوب',
        'optional': 'اختياري',
        'example': 'مثال',
        'home': 'الرئيسية',
        'settings': 'الإعدادات',
        'about': 'حول',
        'contact': 'اتصل بنا',
        'privacy': 'الخصوصية',
        'terms': 'الشروط',
        'copyright': 'جميع الحقوق محفوظة',
        
        # Time and Date
        'today': 'اليوم',
        'yesterday': 'أمس',
        'this_week': 'هذا الأسبوع',
        'this_month': 'هذا الشهر',
        'this_year': 'هذا العام',
        'seconds_ago': 'منذ ثوانٍ',
        'minutes_ago': 'منذ دقائق',
        'hours_ago': 'منذ ساعات',
        'days_ago': 'منذ أيام',
        
        # Camera Types in Arabic
        'rtsp': 'RTSP',
        'usb': 'USB',
        'dahua': 'داهوا',
        'hikvision': 'هيكفيجن',
        'onvif': 'ONVIF',
        'http': 'HTTP'
    }
}

def get_text(key, lang='ar', default=None):
    """Get translated text for a given key and language"""
    if lang in TRANSLATIONS and key in TRANSLATIONS[lang]:
        return TRANSLATIONS[lang][key]
    return default or key

def get_direction(lang='ar'):
    """Get text direction for language"""
    rtl_languages = ['ar', 'he', 'fa', 'ur']
    return 'rtl' if lang in rtl_languages else 'ltr'

def get_language_name(lang_code):
    """Get language name"""
    languages = {
        'ar': 'العربية',
        'en': 'English'
    }
    return languages.get(lang_code, lang_code)
