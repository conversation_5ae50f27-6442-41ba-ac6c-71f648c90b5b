#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎥 نظام المراقبة العربي المتكامل
Arabic Surveillance System - Complete Version
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
import json
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'arabic-surveillance-system-2024'

# بيانات النظام
users = {'admin': 'admin123'}
cameras_data = [
    {
        'id': 1, 
        'name': 'كاميرا المدخل الرئيسي', 
        'type': 'RTSP', 
        'status': 'نشط', 
        'location': 'المدخل الرئيسي',
        'connection': 'rtsp://admin:pass@192.168.1.100:554/stream1',
        'created': '2024-01-15'
    },
    {
        'id': 2, 
        'name': 'كاميرا موقف السيارات', 
        'type': 'USB', 
        'status': 'غير نشط', 
        'location': 'موقف السيارات',
        'connection': '0',
        'created': '2024-01-16'
    },
    {
        'id': 3, 
        'name': 'كاميرا الحديقة الخلفية', 
        'type': 'داهوا', 
        'status': 'نشط', 
        'location': 'الحديقة الخلفية',
        'connection': 'rtsp://admin:pass@192.168.1.101:554/cam/realmonitor?channel=1&subtype=0',
        'created': '2024-01-17'
    },
    {
        'id': 4, 
        'name': 'كاميرا المكتب', 
        'type': 'هيكفيجن', 
        'status': 'نشط', 
        'location': 'المكتب الرئيسي',
        'connection': 'rtsp://admin:pass@192.168.1.102:554/Streaming/Channels/101',
        'created': '2024-01-18'
    }
]

# قالب تسجيل الدخول
LOGIN_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المراقبة العربي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 450px;
            width: 100%;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        .login-body { padding: 2rem; }
        .form-control { 
            text-align: right; 
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .system-icon { 
            font-size: 4rem; 
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="system-icon">🎥</div>
            <h2 class="mb-3">نظام المراقبة العربي</h2>
            <p class="mb-0">يرجى تسجيل الدخول للمتابعة</p>
        </div>
        <div class="login-body">
            {% if error %}
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                </div>
            {% endif %}
            
            <form method="POST">
                <div class="mb-4">
                    <label class="form-label fw-bold">
                        <i class="fas fa-user me-2"></i>اسم المستخدم
                    </label>
                    <input type="text" class="form-control" name="username" placeholder="أدخل اسم المستخدم" required>
                </div>
                <div class="mb-4">
                    <label class="form-label fw-bold">
                        <i class="fas fa-lock me-2"></i>كلمة المرور
                    </label>
                    <input type="password" class="form-control" name="password" placeholder="أدخل كلمة المرور" required>
                </div>
                <button type="submit" class="btn btn-primary btn-login w-100 mb-3">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </button>
            </form>
            
            <div class="alert alert-info text-center">
                <strong>🔑 بيانات الدخول الافتراضية:</strong><br>
                <code>admin</code> / <code>admin123</code>
            </div>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
</body>
</html>
"""

# قالب لوحة التحكم
DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام المراقبة العربي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background-color: #f8f9fa; 
        }
        .navbar { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card { 
            border: none; 
            border-radius: 15px; 
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .card-header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            border-radius: 15px 15px 0 0 !important; 
            border: none;
        }
        .stat-card { 
            transition: all 0.3s ease; 
            cursor: pointer;
        }
        .stat-card:hover { 
            transform: translateY(-10px); 
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .camera-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .camera-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .camera-preview {
            height: 200px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            position: relative;
        }
        .camera-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-active { 
            background: #28a745; 
            color: white;
        }
        .status-inactive { 
            background: #6c757d; 
            color: white;
        }
        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            text-align: center;
        }
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="{{ url_for('dashboard') }}">
                <i class="fas fa-video me-2"></i>نظام المراقبة العربي
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('cameras') }}">
                            <i class="fas fa-camera me-1"></i>الكاميرات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('add_camera') }}">
                            <i class="fas fa-plus me-1"></i>إضافة كاميرا
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ session.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-user-cog me-2"></i>الملف الشخصي
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- رسائل التأكيد -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- قسم الترحيب -->
        <div class="welcome-section">
            <h1 class="display-4 mb-3">🎥 مرحباً بك في نظام المراقبة</h1>
            <p class="lead">نظام مراقبة متطور يدعم اللغة العربية بالكامل</p>
            <p class="mb-0">
                <i class="fas fa-clock me-2"></i>
                {{ current_time }}
            </p>
        </div>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-camera fa-3x mb-3"></i>
                        <h2 class="display-4">{{ total_cameras }}</h2>
                        <p class="mb-0 fs-5">إجمالي الكاميرات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-play-circle fa-3x mb-3"></i>
                        <h2 class="display-4">{{ active_cameras }}</h2>
                        <p class="mb-0 fs-5">الكاميرات النشطة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-white stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-pause-circle fa-3x mb-3"></i>
                        <h2 class="display-4">{{ offline_cameras }}</h2>
                        <p class="mb-0 fs-5">الكاميرات غير المتصلة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt fa-3x mb-3"></i>
                        <h2 class="display-4">100%</h2>
                        <p class="mb-0 fs-5">أمان النظام</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- قائمة الكاميرات -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-video me-2"></i>الكاميرات المتاحة
                        </h4>
                    </div>
                    <div class="card-body">
                        {% if cameras %}
                            <div class="row">
                                {% for camera in cameras %}
                                <div class="col-lg-6 col-md-6 mb-4">
                                    <div class="camera-card">
                                        <div class="camera-preview">
                                            <span class="camera-status status-{{ 'active' if camera.status == 'نشط' else 'inactive' }}">
                                                {{ camera.status }}
                                            </span>
                                            <i class="fas fa-video"></i>
                                        </div>
                                        <div class="p-3">
                                            <h5 class="card-title">{{ camera.name }}</h5>
                                            <div class="row mb-2">
                                                <div class="col-6">
                                                    <small class="text-muted">النوع:</small><br>
                                                    <span class="badge bg-secondary">{{ camera.type }}</span>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">تاريخ الإضافة:</small><br>
                                                    <small>{{ camera.created }}</small>
                                                </div>
                                            </div>
                                            <p class="text-muted mb-3">
                                                <i class="fas fa-map-marker-alt me-1"></i>{{ camera.location }}
                                            </p>
                                            <div class="btn-group w-100">
                                                <button class="btn btn-primary btn-sm" onclick="viewCamera({{ camera.id }})">
                                                    <i class="fas fa-eye"></i> عرض
                                                </button>
                                                <button class="btn btn-info btn-sm" onclick="takeSnapshot({{ camera.id }})">
                                                    <i class="fas fa-camera"></i> صورة
                                                </button>
                                                <button class="btn btn-warning btn-sm" onclick="editCamera({{ camera.id }})">
                                                    <i class="fas fa-cog"></i> إعدادات
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-camera fa-5x text-muted mb-4"></i>
                                <h3 class="text-muted">لا توجد كاميرات مُكوّنة</h3>
                                <p class="text-muted fs-5">أضف أول كاميرا لبدء المراقبة</p>
                                <a href="{{ url_for('add_camera') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus me-2"></i>إضافة كاميرا جديدة
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- الإجراءات السريعة -->
            <div class="col-lg-4">
                <div class="quick-actions">
                    <h5 class="mb-3">
                        <i class="fas fa-bolt me-2"></i>الإجراءات السريعة
                    </h5>
                    <div class="d-grid gap-3">
                        <a href="{{ url_for('add_camera') }}" class="action-btn">
                            <i class="fas fa-plus fa-2x d-block mb-2"></i>
                            إضافة كاميرا جديدة
                        </a>
                        <a href="{{ url_for('cameras') }}" class="action-btn">
                            <i class="fas fa-list fa-2x d-block mb-2"></i>
                            إدارة الكاميرات
                        </a>
                        <a href="#" class="action-btn" onclick="refreshSystem()">
                            <i class="fas fa-sync-alt fa-2x d-block mb-2"></i>
                            تحديث النظام
                        </a>
                        <a href="#" class="action-btn" onclick="exportData()">
                            <i class="fas fa-download fa-2x d-block mb-2"></i>
                            تصدير البيانات
                        </a>
                    </div>
                </div>

                <!-- معلومات النظام -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>معلومات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <strong>الإصدار:</strong> 1.0.0
                            </li>
                            <li class="mb-2">
                                <strong>اللغة:</strong> العربية
                            </li>
                            <li class="mb-2">
                                <strong>المستخدم:</strong> {{ session.username }}
                            </li>
                            <li class="mb-2">
                                <strong>الحالة:</strong> 
                                <span class="badge bg-success">متصل</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // وظائف JavaScript
        function viewCamera(id) {
            alert('عرض الكاميرا رقم ' + id);
        }

        function takeSnapshot(id) {
            alert('تم التقاط صورة من الكاميرا رقم ' + id);
        }

        function editCamera(id) {
            alert('تعديل إعدادات الكاميرا رقم ' + id);
        }

        function refreshSystem() {
            alert('جاري تحديث النظام...');
            location.reload();
        }

        function exportData() {
            alert('جاري تصدير البيانات...');
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card, .camera-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
"""

# الصفحات
@app.route('/')
def index():
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    error = None
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if username in users and users[username] == password:
            session['logged_in'] = True
            session['username'] = username
            flash('تم تسجيل الدخول بنجاح! مرحباً بك في نظام المراقبة العربي', 'success')
            return redirect(url_for('dashboard'))
        else:
            error = 'اسم المستخدم أو كلمة المرور غير صحيحة'
    
    return render_template_string(LOGIN_TEMPLATE, error=error)

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح. نراك قريباً!', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'logged_in' not in session:
        return redirect(url_for('login'))
    
    total_cameras = len(cameras_data)
    active_cameras = len([c for c in cameras_data if c['status'] == 'نشط'])
    offline_cameras = total_cameras - active_cameras
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    return render_template_string(DASHBOARD_TEMPLATE, 
                                cameras=cameras_data,
                                total_cameras=total_cameras,
                                active_cameras=active_cameras,
                                offline_cameras=offline_cameras,
                                current_time=current_time)

@app.route('/cameras')
def cameras():
    if 'logged_in' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))

@app.route('/add_camera')
def add_camera():
    if 'logged_in' not in session:
        return redirect(url_for('login'))
    
    # إضافة كاميرا وهمية للعرض
    new_camera = {
        'id': len(cameras_data) + 1,
        'name': f'كاميرا جديدة {len(cameras_data) + 1}',
        'type': 'RTSP',
        'status': 'نشط',
        'location': 'موقع جديد',
        'connection': 'rtsp://admin:pass@*************:554/stream1',
        'created': datetime.now().strftime('%Y-%m-%d')
    }
    cameras_data.append(new_camera)
    flash('تم إضافة الكاميرا الجديدة بنجاح! يمكنك الآن تكوين إعداداتها', 'success')
    return redirect(url_for('dashboard'))

if __name__ == '__main__':
    print("🎥 نظام المراقبة العربي المتكامل")
    print("=" * 60)
    print("📍 الخادم: http://localhost:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("🌐 اللغة: العربية الكاملة مع دعم RTL")
    print("✨ التصميم: متجاوب وجميل مع تأثيرات بصرية")
    print("🔧 المميزات: إدارة كاميرات، إحصائيات، واجهة تفاعلية")
    print("=" * 60)
    print("🚀 النظام جاهز للاستخدام!")
    print()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
