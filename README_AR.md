# 🎥 نظام المراقبة متعدد الأنواع

نظام مراقبة شامل يعمل عبر الويب مبني بـ Python Flask يدعم أنواع متعددة من الكاميرات بما في ذلك داهوا، هيكفيجن، كاميرات IP، تدفقات RTSP، كاميرات USB، والأجهزة المتوافقة مع ONVIF.

## ✨ المميزات

### 🎯 الوظائف الأساسية
- **دعم متعدد الكاميرات**: RTSP، USB، داهوا، هيكفيجن، ONVIF، HTTP/MJPEG
- **البث المباشر**: تدفقات فيديو في الوقت الفعلي مع زمن استجابة منخفض
- **كشف الحركة**: كشف تلقائي للحركة مع التنبيهات
- **التسجيل**: تسجيل الفيديو مع مشغلات الحركة
- **اللقطات**: التقاط الصور يدوياً وتلقائياً
- **الخريطة التفاعلية**: تحديد مواقع الكاميرات بإحداثيات GPS

### 🔐 الأمان والمصادقة
- **إدارة المستخدمين**: أدوار المدير والمدير التنفيذي والمشاهد
- **تسجيل دخول آمن**: مصادقة بكلمة مرور مع إدارة الجلسات
- **نظام الصلاحيات**: تحكم في الوصول حسب الأدوار
- **سجل تسجيل الدخول**: تتبع وصول المستخدمين والمحاولات الفاشلة

### 🌐 واجهة الويب
- **تصميم متجاوب**: يعمل على سطح المكتب والجهاز اللوحي والجوال
- **دعم RTL**: دعم كامل للغة العربية
- **تحديثات فورية**: تحديثات مباشرة باستخدام WebSocket
- **عرض الشبكة**: شبكة كاميرات قابلة للتخصيص (1x1، 2x2، 3x3، 4x4)
- **وضع ملء الشاشة**: عرض الكاميرا بملء الشاشة

### 📊 مميزات الإدارة
- **إدارة الكاميرات**: إضافة وتعديل وحذف وتكوين الكاميرات
- **مراقبة الحالة**: تتبع حالة الكاميرا في الوقت الفعلي
- **معالجة الأخطاء**: تقارير شاملة للأخطاء والسجلات
- **تخزين قاعدة البيانات**: قاعدة بيانات SQLite للتكوين والسجلات

## 🚀 البدء السريع

### المتطلبات المسبقة
- Python 3.8 أو أحدث
- pip (مثبت حزم Python)
- Git (لاستنساخ المستودع)

### التثبيت

1. **استنساخ المستودع**
   ```bash
   git clone <repository-url>
   cd surveillance-system
   ```

2. **تثبيت التبعيات**
   ```bash
   pip install -r requirements.txt
   ```

3. **تشغيل التطبيق العربي**
   ```bash
   python app_arabic.py
   ```

4. **الوصول للنظام**
   - افتح متصفح الويب
   - انتقل إلى `http://localhost:5000`
   - سجل الدخول ببيانات الاعتماد الافتراضية: `admin` / `admin123`

## 📋 متطلبات النظام

### متطلبات الأجهزة
- **الحد الأدنى**: 2GB RAM، 1GB تخزين
- **المُوصى به**: 4GB RAM، 10GB تخزين
- **الشبكة**: اتصال إنترنت مستقر لكاميرات IP

### متطلبات البرمجيات
- **نظام التشغيل**: Windows، Linux، أو macOS
- **Python**: 3.8+
- **المتصفح**: Chrome، Firefox، Safari، أو Edge (أحدث الإصدارات)

## 🎥 أنواع الكاميرات المدعومة

### 1. كاميرات RTSP
```
سلسلة الاتصال: rtsp://username:password@*************:554/stream1
```

### 2. كاميرات USB
```
سلسلة الاتصال: 0 (رقم الجهاز)
```

### 3. كاميرات داهوا
```
سلسلة الاتصال: rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0
```

### 4. كاميرات هيكفيجن
```
سلسلة الاتصال: rtsp://admin:password@*************:554/Streaming/Channels/101
```

### 5. كاميرات ONVIF
```
سلسلة الاتصال: onvif://*************:80
```

### 6. تدفقات HTTP/MJPEG
```
سلسلة الاتصال: http://*************:8080/video
```

## 🔧 التكوين

### متغيرات البيئة
أنشئ ملف `.env` في الدليل الجذر:

```env
SECRET_KEY=مفتاحك-السري-هنا
DATABASE_URL=sqlite:///surveillance.db
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
```

### تكوين الكاميرا
1. انتقل إلى **الكاميرات** ← **إضافة كاميرا**
2. املأ المعلومات المطلوبة:
   - **الاسم**: اسم وصفي للكاميرا
   - **النوع**: اختر نوع الكاميرا
   - **سلسلة الاتصال**: URL الكاميرا أو معرف الجهاز
   - **بيانات الاعتماد**: اسم المستخدم وكلمة المرور (إذا لزم الأمر)
   - **الموقع**: إحداثيات GPS واسم الموقع

### إدارة المستخدمين
1. انتقل إلى **المستخدمين** (المدير فقط)
2. أنشئ مستخدمين جدد بالأدوار المناسبة:
   - **المدير**: وصول كامل للنظام
   - **المدير التنفيذي**: إدارة الكاميرات والعرض
   - **المشاهد**: وصول للعرض فقط

## 🗺️ إعداد الخريطة التفاعلية

### استخدام OpenStreetMap (افتراضي)
- لا حاجة لتكوين
- يعمل مباشرة
- مجاني ومفتوح المصدر

### إعداد موقع الكاميرا
1. ابحث عن إحداثيات GPS للكاميرا باستخدام خرائط جوجل
2. أضف الإحداثيات عند إنشاء/تعديل الكاميرات
3. اعرض جميع الكاميرات على الخريطة التفاعلية

## 👥 إدارة المستخدمين

### إنشاء المستخدمين
1. سجل الدخول كمدير
2. اذهب إلى قسم **المستخدمين**
3. أنشئ مستخدمين بالأدوار المناسبة:
   - **المدير**: وصول كامل للنظام
   - **المدير التنفيذي**: يمكن إدارة الكاميرات
   - **المشاهد**: وصول للعرض فقط

## 🔒 إعداد الأمان

### تغيير كلمة المرور الافتراضية
1. اذهب إلى **الملف الشخصي** ← **تغيير كلمة المرور**
2. استخدم كلمة مرور قوية تحتوي على:
   - 8 أحرف على الأقل
   - مزيج من الأحرف والأرقام والرموز

### أمان الشبكة
- استخدم HTTPS في الإنتاج
- قم بإعداد قواعد جدار الحماية
- استخدم VPN للوصول عن بُعد
- قم بتقسيم شبكة الكاميرا

## 📱 الوصول عبر الجوال

النظام متجاوب بالكامل ويعمل على:
- **الهواتف الذكية** (iOS/Android)
- **الأجهزة اللوحية**
- **متصفحات سطح المكتب**

## 🛠️ استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة

#### أخطاء "الوحدة غير موجودة"
```bash
# تثبيت التبعيات المفقودة
pip install [اسم-الوحدة-المفقودة]
```

#### عدم اتصال الكاميرا
1. تحقق من اتصال الشبكة
2. تحقق من بيانات اعتماد الكاميرا
3. اختبر URL RTSP في مشغل VLC
4. تحقق من إعدادات جدار الحماية

#### أخطاء قاعدة البيانات
```bash
# إعادة تعيين قاعدة البيانات (تحذير: يحذف جميع البيانات)
rm surveillance.db
python app_arabic.py
```

#### المنفذ مستخدم بالفعل
```bash
# العثور على العملية وإنهاؤها
netstat -ano | findstr :5000
taskkill /PID [معرف-العملية] /F
```

## 📊 متطلبات النظام

### الحد الأدنى للمتطلبات
- **الذاكرة**: 2GB
- **التخزين**: 1GB مساحة فارغة
- **الشبكة**: اتصال إنترنت مستقر
- **نظام التشغيل**: Windows 10+، macOS 10.14+، أو Linux

### المُوصى به للإنتاج
- **الذاكرة**: 4GB+
- **التخزين**: 10GB+ (للتسجيلات)
- **المعالج**: معالج متعدد النوى
- **الشبكة**: إيثرنت جيجابت

## 🔄 التحديثات والصيانة

### الصيانة الدورية
- راقب مساحة القرص للتسجيلات
- تحقق من اتصال الكاميرا
- راجع سجلات وصول المستخدمين
- حدث التبعيات بانتظام

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
cp surveillance.db surveillance_backup_$(date +%Y%m%d).db

# نسخ احتياطي للتسجيلات
tar -czf recordings_backup_$(date +%Y%m%d).tar.gz recordings/
```

## 📞 الدعم

### الحصول على المساعدة
1. تحقق من قسم استكشاف الأخطاء وإصلاحها
2. راجع README.md الرئيسي
3. تحقق من سجلات النظام في التطبيق
4. أنشئ مشكلة على GitHub

### ملفات السجل
- سجلات التطبيق: إخراج وحدة التحكم
- أخطاء الكاميرا: متاحة في إدارة الكاميرا
- حالة النظام: صفحة لوحة التحكم

## 🎯 الخطوات التالية

بعد التثبيت:
1. **أضف كاميراتك** باستخدام واجهة الويب
2. **كوّن كشف الحركة** للمناطق المهمة
3. **أعد حسابات المستخدمين** لفريقك
4. **اختبر النظام** مع أنواع مختلفة من الكاميرات
5. **كوّن الإشعارات** (إذا لزم الأمر)

## 🌟 المميزات الخاصة باللغة العربية

### ✅ دعم كامل للغة العربية
- **واجهة مستخدم عربية**: جميع النصوص والقوائم باللغة العربية
- **دعم RTL**: تخطيط من اليمين إلى اليسار
- **خطوط عربية**: خط Cairo الجميل والواضح
- **تواريخ عربية**: تنسيق التواريخ والأوقات بالعربية
- **أرقام عربية**: عرض الأرقام بالأرقام العربية
- **رسائل خطأ عربية**: جميع رسائل الخطأ والتأكيد بالعربية

### 🎨 تصميم عربي
- **ألوان متناسقة**: تدرجات لونية جميلة
- **أيقونات واضحة**: أيقونات Font Awesome مع نصوص عربية
- **تخطيط متجاوب**: يعمل بشكل مثالي على جميع الأجهزة
- **انيميشن سلس**: تأثيرات بصرية جميلة

---

**🎉 تهانينا! نظام المراقبة العربي جاهز للاستخدام.**

للحصول على الوثائق التفصيلية، راجع [README.md](README.md)
