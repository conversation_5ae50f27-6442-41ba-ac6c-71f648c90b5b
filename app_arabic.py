#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المراقبة باللغة العربية
Arabic Surveillance System - Full Arabic Support
"""

from flask import Flask, render_template, Response, request, jsonify, redirect, url_for, flash, session
from flask_login import LoginManager, login_required, current_user
import os
import logging
from datetime import datetime

# Import our modules
from config import config
from models import db, User, Camera, CameraType, CameraStatus, UserRole
from auth import auth_bp, require_permission
from translations import get_text, get_direction, TRANSLATIONS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Force Arabic as default
    app.config['DEFAULT_LANGUAGE'] = 'ar'
    app.config['LANGUAGES'] = ['ar', 'en']
    
    # Initialize extensions
    db.init_app(app)
    
    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    # Create directories
    os.makedirs(app.config['RECORDING_PATH'], exist_ok=True)
    os.makedirs(app.config['SNAPSHOT_PATH'], exist_ok=True)
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    # Language support
    @app.before_request
    def before_request():
        # Force Arabic language
        session['language'] = 'ar'
    
    @app.context_processor
    def inject_language():
        return {
            'current_language': 'ar',
            'get_text': get_text,
            'get_direction': get_direction,
            'translations': TRANSLATIONS.get('ar', {})
        }
    
    # Routes
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('auth.login'))
    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        cameras = Camera.query.all()
        active_cameras = Camera.query.filter_by(status=CameraStatus.ACTIVE).count()
        total_cameras = Camera.query.count()
        
        return render_template('dashboard_ar.html', 
                             cameras=cameras,
                             active_cameras=active_cameras,
                             total_cameras=total_cameras)
    
    @app.route('/cameras')
    @login_required
    def cameras():
        cameras = Camera.query.all()
        return render_template('cameras_ar.html', cameras=cameras)
    
    @app.route('/cameras/add', methods=['GET', 'POST'])
    @login_required
    @require_permission('manage')
    def add_camera():
        if request.method == 'POST':
            try:
                camera = Camera(
                    name=request.form['name'],
                    description=request.form.get('description', ''),
                    camera_type=CameraType(request.form['camera_type']),
                    connection_string=request.form['connection_string'],
                    username=request.form.get('username'),
                    password=request.form.get('password'),
                    ip_address=request.form.get('ip_address'),
                    port=int(request.form['port']) if request.form.get('port') else None,
                    latitude=float(request.form['latitude']) if request.form.get('latitude') else None,
                    longitude=float(request.form['longitude']) if request.form.get('longitude') else None,
                    location_name=request.form.get('location_name'),
                    motion_detection=bool(request.form.get('motion_detection')),
                    is_recording=bool(request.form.get('is_recording')),
                    created_by=current_user.id
                )
                
                db.session.add(camera)
                db.session.commit()
                
                flash('تم إضافة الكاميرا بنجاح!', 'success')
                return redirect(url_for('cameras'))
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error adding camera: {str(e)}")
                flash('فشل في إضافة الكاميرا. يرجى التحقق من البيانات المدخلة.', 'error')
        
        return render_template('add_camera_ar.html', camera_types=CameraType)
    
    @app.route('/cameras/<int:camera_id>/edit', methods=['GET', 'POST'])
    @login_required
    @require_permission('manage')
    def edit_camera(camera_id):
        camera = Camera.query.get_or_404(camera_id)
        
        if request.method == 'POST':
            try:
                camera.name = request.form['name']
                camera.description = request.form.get('description', '')
                camera.camera_type = CameraType(request.form['camera_type'])
                camera.connection_string = request.form['connection_string']
                camera.username = request.form.get('username')
                camera.password = request.form.get('password')
                camera.ip_address = request.form.get('ip_address')
                camera.port = int(request.form['port']) if request.form.get('port') else None
                camera.latitude = float(request.form['latitude']) if request.form.get('latitude') else None
                camera.longitude = float(request.form['longitude']) if request.form.get('longitude') else None
                camera.location_name = request.form.get('location_name')
                camera.motion_detection = bool(request.form.get('motion_detection'))
                camera.is_recording = bool(request.form.get('is_recording'))
                camera.updated_at = datetime.utcnow()
                
                db.session.commit()
                
                flash('تم تحديث الكاميرا بنجاح!', 'success')
                return redirect(url_for('cameras'))
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error updating camera: {str(e)}")
                flash('فشل في تحديث الكاميرا. يرجى التحقق من البيانات المدخلة.', 'error')
        
        return render_template('edit_camera_ar.html', camera=camera, camera_types=CameraType)
    
    @app.route('/cameras/<int:camera_id>/delete', methods=['POST'])
    @login_required
    @require_permission('manage')
    def delete_camera(camera_id):
        try:
            camera = Camera.query.get_or_404(camera_id)
            db.session.delete(camera)
            db.session.commit()
            
            return jsonify({'success': True, 'message': 'تم حذف الكاميرا بنجاح'})
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting camera: {str(e)}")
            return jsonify({'success': False, 'message': 'فشل في حذف الكاميرا'}), 500
    
    @app.route('/cameras/<int:camera_id>/start', methods=['POST'])
    @login_required
    def start_camera(camera_id):
        try:
            camera = Camera.query.get_or_404(camera_id)
            camera.status = CameraStatus.ACTIVE
            camera.last_seen = datetime.utcnow()
            db.session.commit()
            return jsonify({'success': True, 'message': 'تم تشغيل الكاميرا بنجاح'})
        except Exception as e:
            logger.error(f"Error starting camera: {str(e)}")
            return jsonify({'success': False, 'message': 'فشل في تشغيل الكاميرا'}), 500
    
    @app.route('/cameras/<int:camera_id>/stop', methods=['POST'])
    @login_required
    def stop_camera(camera_id):
        try:
            camera = Camera.query.get_or_404(camera_id)
            camera.status = CameraStatus.INACTIVE
            db.session.commit()
            return jsonify({'success': True, 'message': 'تم إيقاف الكاميرا بنجاح'})
        except Exception as e:
            logger.error(f"Error stopping camera: {str(e)}")
            return jsonify({'success': False, 'message': 'فشل في إيقاف الكاميرا'}), 500
    
    @app.route('/video_feed/<int:camera_id>')
    @login_required
    def video_feed(camera_id):
        # Placeholder for video feed
        def generate():
            yield b'--frame\r\nContent-Type: text/plain\r\n\r\nCamera feed placeholder\r\n'
        
        return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')
    
    @app.route('/map')
    @login_required
    def map_view():
        cameras = Camera.query.filter(Camera.latitude.isnot(None), Camera.longitude.isnot(None)).all()
        import json
        cameras_data = [camera.to_dict() for camera in cameras]
        return render_template('map_ar.html', cameras=json.dumps(cameras_data))
    
    @app.route('/api/cameras')
    @login_required
    def api_cameras():
        cameras = Camera.query.all()
        return jsonify([camera.to_dict() for camera in cameras])
    
    @app.route('/api/cameras/<int:camera_id>/snapshot', methods=['POST'])
    @login_required
    def take_snapshot(camera_id):
        try:
            return jsonify({'success': True, 'message': 'تم التقاط الصورة بنجاح'})
        except Exception as e:
            logger.error(f"Error taking snapshot: {str(e)}")
            return jsonify({'success': False, 'message': 'فشل في التقاط الصورة'}), 500
    
    # Error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404_ar.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500_ar.html'), 500
    
    # Create database tables
    with app.app_context():
        db.create_all()
        
        # Create default admin user if no users exist
        if User.query.count() == 0:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role=UserRole.ADMIN
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            logger.info("تم إنشاء مستخدم المدير الافتراضي: admin/admin123")
    
    return app

if __name__ == '__main__':
    app = create_app()
    
    print("🎥 نظام المراقبة باللغة العربية")
    print("=" * 50)
    print("📍 الخادم: http://localhost:5000")
    print("👤 تسجيل الدخول الافتراضي: admin / admin123")
    print("🌐 اللغة: العربية")
    print("=" * 50)
    print("📝 اضغط Ctrl+C لإيقاف الخادم")
    print()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
