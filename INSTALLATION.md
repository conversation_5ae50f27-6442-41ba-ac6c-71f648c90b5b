# 🚀 Installation Guide - Multi-Type Camera Surveillance System

## 📋 Quick Start

### 1. Prerequisites
- **Python 3.8+** installed on your system
- **pip** package manager
- **Web browser** (Chrome, Firefox, Safari, or Edge)

### 2. Basic Installation

```bash
# 1. Navigate to the project directory
cd surveillance-system

# 2. Install basic dependencies
pip install Flask Flask-SQLAlchemy Flask-Login python-dotenv

# 3. Test the system
python test_app.py
```

### 3. Access the Test System
- Open your browser and go to: `http://localhost:5000`
- You should see the surveillance system status page

## 🔧 Full Installation

### Step 1: Install All Dependencies

```bash
# Install core dependencies
pip install Flask Flask-SQLAlchemy Flask-Login Flask-WTF python-dotenv

# Install camera and video processing
pip install opencv-python numpy Pillow

# Install web socket support (optional)
pip install flask-socketio python-socketio

# Install additional utilities
pip install requests imutils
```

### Step 2: Configuration

1. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` file with your settings:**
   ```env
   SECRET_KEY=your-unique-secret-key-here
   DATABASE_URL=sqlite:///surveillance.db
   ```

### Step 3: Run the Full Application

```bash
# Option 1: Using the run script
python run.py

# Option 2: Direct execution
python app.py
```

### Step 4: First Login

1. Open browser: `http://localhost:5000`
2. Login with default credentials:
   - **Username:** `admin`
   - **Password:** `admin123`
3. **Important:** Change the default password immediately!

## 🎥 Adding Your First Camera

### RTSP Camera Example
1. Go to **Cameras** → **Add Camera**
2. Fill in the details:
   - **Name:** "Front Door Camera"
   - **Type:** RTSP
   - **Connection String:** `rtsp://admin:password@*************:554/stream1`
   - **Location:** Add GPS coordinates if available

### USB Camera Example
1. **Name:** "USB Webcam"
2. **Type:** USB
3. **Connection String:** `0` (for first USB camera)

### IP Camera Examples

#### Dahua Camera
```
Connection String: rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0
```

#### Hikvision Camera
```
Connection String: rtsp://admin:password@*************:554/Streaming/Channels/101
```

#### ONVIF Camera
```
Connection String: onvif://*************:80
Username: admin
Password: your-camera-password
```

## 🗺️ Setting Up the Map

### Using Default OpenStreetMap
- No configuration needed
- Works out of the box
- Add GPS coordinates to cameras to see them on the map

### Camera Location Setup
1. Find your camera's GPS coordinates using Google Maps
2. Add coordinates when creating/editing cameras
3. View all cameras on the interactive map

## 👥 User Management

### Creating Users
1. Login as admin
2. Go to **Users** section
3. Create users with appropriate roles:
   - **Admin:** Full system access
   - **Manager:** Can manage cameras
   - **Viewer:** View-only access

## 🔒 Security Setup

### Change Default Password
1. Go to **Profile** → **Change Password**
2. Use a strong password with:
   - At least 8 characters
   - Mix of letters, numbers, and symbols

### Network Security
- Use HTTPS in production
- Set up firewall rules
- Use VPN for remote access
- Segment camera network

## 📱 Mobile Access

The system is fully responsive and works on:
- **Smartphones** (iOS/Android)
- **Tablets**
- **Desktop browsers**

## 🛠️ Troubleshooting

### Common Issues

#### "Module not found" errors
```bash
# Install missing dependencies
pip install [missing-module-name]
```

#### Camera not connecting
1. Check network connectivity
2. Verify camera credentials
3. Test RTSP URL in VLC media player
4. Check firewall settings

#### Database errors
```bash
# Reset database (WARNING: This deletes all data)
rm surveillance.db
python app.py
```

#### Port already in use
```bash
# Find and kill process using port 5000
netstat -ano | findstr :5000
taskkill /PID [process-id] /F
```

## 📊 System Requirements

### Minimum Requirements
- **RAM:** 2GB
- **Storage:** 1GB free space
- **Network:** Stable internet connection
- **OS:** Windows 10+, macOS 10.14+, or Linux

### Recommended for Production
- **RAM:** 4GB+
- **Storage:** 10GB+ (for recordings)
- **CPU:** Multi-core processor
- **Network:** Gigabit ethernet

## 🔄 Updates and Maintenance

### Regular Maintenance
- Monitor disk space for recordings
- Check camera connectivity
- Review user access logs
- Update dependencies regularly

### Backup
```bash
# Backup database
cp surveillance.db surveillance_backup_$(date +%Y%m%d).db

# Backup recordings
tar -czf recordings_backup_$(date +%Y%m%d).tar.gz recordings/
```

## 📞 Support

### Getting Help
1. Check the troubleshooting section
2. Review the main README.md
3. Check system logs in the application
4. Create an issue on GitHub

### Log Files
- Application logs: Console output
- Camera errors: Available in camera management
- System status: Dashboard page

## 🎯 Next Steps

After installation:
1. **Add your cameras** using the web interface
2. **Configure motion detection** for important areas
3. **Set up user accounts** for your team
4. **Test the system** with different camera types
5. **Configure notifications** (if needed)

---

**🎉 Congratulations! Your surveillance system is ready to use.**

For detailed documentation, see [README.md](README.md)
