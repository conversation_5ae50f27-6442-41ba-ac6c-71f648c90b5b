import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-this'
    
    # Database Configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///surveillance.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Camera Configuration
    MAX_CAMERAS = 50
    STREAM_TIMEOUT = 30
    RECORDING_PATH = 'recordings'
    SNAPSHOT_PATH = 'snapshots'
    
    # Video Configuration
    VIDEO_WIDTH = 640
    VIDEO_HEIGHT = 480
    VIDEO_FPS = 30
    
    # Motion Detection
    MOTION_DETECTION_ENABLED = True
    MOTION_THRESHOLD = 500
    
    # Notification Settings
    EMAIL_NOTIFICATIONS = False
    SMTP_SERVER = os.environ.get('SMTP_SERVER')
    SMTP_PORT = int(os.environ.get('SMTP_PORT', 587))
    EMAIL_USERNAME = os.environ.get('EMAIL_USERNAME')
    EMAIL_PASSWORD = os.environ.get('EMAIL_PASSWORD')
    
    # Map Configuration
    DEFAULT_MAP_CENTER_LAT = 24.7136
    DEFAULT_MAP_CENTER_LNG = 46.6753  # Riyadh coordinates
    DEFAULT_MAP_ZOOM = 10
    
    # Language Support
    LANGUAGES = ['ar', 'en']
    DEFAULT_LANGUAGE = 'ar'
    BABEL_DEFAULT_LOCALE = 'ar'
    BABEL_DEFAULT_TIMEZONE = 'Asia/Riyadh'
    
    # Security
    SESSION_TIMEOUT = 3600  # 1 hour
    MAX_LOGIN_ATTEMPTS = 5
    
    # File Upload
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    UPLOAD_FOLDER = 'uploads'
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

class DevelopmentConfig(Config):
    DEBUG = True
    
class ProductionConfig(Config):
    DEBUG = False
    
class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///test.db'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
