# 🎥 Multi-Type Camera Surveillance System

A comprehensive web-based surveillance system built with Python Flask that supports multiple camera types including Dahua, Hikvision, IP cameras, RTSP streams, USB cameras, and ONVIF-compliant devices.

## ✨ Features

### 🎯 Core Functionality
- **Multi-Camera Support**: RTSP, USB, Dahua, Hikvision, ONVIF, HTTP/MJPEG
- **Live Streaming**: Real-time video feeds with low latency
- **Motion Detection**: Automatic motion detection with alerts
- **Recording**: Video recording with motion triggers
- **Snapshots**: Manual and automatic snapshot capture
- **Interactive Map**: Camera positioning with GPS coordinates

### 🔐 Security & Authentication
- **User Management**: Admin, Manager, and Viewer roles
- **Secure Login**: Password-based authentication with session management
- **Permission System**: Role-based access control
- **Login History**: Track user access and failed attempts

### 🌐 Web Interface
- **Responsive Design**: Works on desktop, tablet, and mobile
- **RTL Support**: Arabic language support
- **Real-time Updates**: WebSocket-based live updates
- **Grid View**: Customizable camera grid (1x1, 2x2, 3x3, 4x4)
- **Fullscreen Mode**: Full-screen camera viewing

### 📊 Management Features
- **Camera Management**: Add, edit, delete, and configure cameras
- **Status Monitoring**: Real-time camera status tracking
- **Error Handling**: Comprehensive error reporting and logging
- **Database Storage**: SQLite database for configuration and logs

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)
- Git (for cloning the repository)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd surveillance-system
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Access the system**
   - Open your web browser
   - Navigate to `http://localhost:5000`
   - Login with default credentials: `admin` / `admin123`

## 📋 System Requirements

### Hardware Requirements
- **Minimum**: 2GB RAM, 1GB storage
- **Recommended**: 4GB RAM, 10GB storage
- **Network**: Stable internet connection for IP cameras

### Software Requirements
- **Operating System**: Windows, Linux, or macOS
- **Python**: 3.8+
- **Browser**: Chrome, Firefox, Safari, or Edge (latest versions)

## 🎥 Supported Camera Types

### 1. RTSP Cameras
```
Connection String: rtsp://username:password@*************:554/stream1
```

### 2. USB Cameras
```
Connection String: 0 (device number)
```

### 3. Dahua Cameras
```
Connection String: rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0
```

### 4. Hikvision Cameras
```
Connection String: rtsp://admin:password@*************:554/Streaming/Channels/101
```

### 5. ONVIF Cameras
```
Connection String: onvif://*************:80
```

### 6. HTTP/MJPEG Streams
```
Connection String: http://*************:8080/video
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:

```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///surveillance.db
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
```

### Camera Configuration
1. Navigate to **Cameras** → **Add Camera**
2. Fill in the required information:
   - **Name**: Descriptive camera name
   - **Type**: Select camera type
   - **Connection String**: Camera URL or device ID
   - **Credentials**: Username and password (if required)
   - **Location**: GPS coordinates and location name

### User Management
1. Navigate to **Users** (Admin only)
2. Create new users with appropriate roles:
   - **Admin**: Full system access
   - **Manager**: Camera management and viewing
   - **Viewer**: View-only access

## 🗺️ Interactive Map Setup

### Using Google Maps
1. Get a Google Maps API key
2. Update the map configuration in `templates/map.html`
3. Replace the OpenStreetMap tiles with Google Maps

### Using OpenStreetMap (Default)
- No API key required
- Works out of the box
- Free and open-source

## 📱 Mobile Support

The system is fully responsive and works on:
- **Smartphones**: iOS and Android
- **Tablets**: iPad and Android tablets
- **Desktop**: All major browsers

### Mobile Features
- Touch-friendly interface
- Swipe gestures for camera navigation
- Optimized video streaming for mobile networks
- Push notifications (when supported)

## 🔍 Troubleshooting

### Common Issues

#### Camera Not Connecting
1. Check network connectivity
2. Verify camera credentials
3. Test RTSP URL in VLC media player
4. Check firewall settings

#### Poor Video Quality
1. Adjust video resolution in camera settings
2. Check network bandwidth
3. Reduce number of simultaneous streams

#### Motion Detection Not Working
1. Ensure motion detection is enabled
2. Adjust motion sensitivity
3. Check camera positioning

### Log Files
- Application logs: Check console output
- Camera errors: View in camera management page
- System logs: Stored in database

## 🛠️ Development

### Project Structure
```
surveillance-system/
├── app.py                 # Main Flask application
├── config.py             # Configuration settings
├── models.py             # Database models
├── auth.py               # Authentication system
├── camera_manager.py     # Camera handling
├── requirements.txt      # Python dependencies
├── templates/            # HTML templates
│   ├── base.html
│   ├── dashboard.html
│   ├── cameras.html
│   └── auth/
├── static/               # Static files
│   ├── css/
│   ├── js/
│   └── img/
└── README.md
```

### Adding New Camera Types
1. Update `CameraType` enum in `models.py`
2. Add connection logic in `camera_manager.py`
3. Update form options in templates
4. Add connection examples

### Database Schema
- **Users**: User accounts and roles
- **Cameras**: Camera configuration
- **Recordings**: Video recordings metadata
- **Snapshots**: Snapshot metadata
- **LoginHistory**: User login tracking
- **SystemLogs**: Application logs

## 🔒 Security Considerations

### Best Practices
- Change default admin password immediately
- Use strong passwords for all accounts
- Enable HTTPS in production
- Regularly update dependencies
- Monitor login attempts
- Backup database regularly

### Network Security
- Use VPN for remote access
- Segment camera network
- Enable camera authentication
- Use encrypted streams when possible

## 📈 Performance Optimization

### For Better Performance
- Use dedicated server for production
- Enable video compression
- Limit concurrent streams
- Use SSD storage for recordings
- Monitor system resources

### Scaling
- Use load balancer for multiple instances
- Implement database clustering
- Use CDN for static files
- Consider microservices architecture

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the documentation

## 🔄 Updates

### Version History
- **v1.0.0**: Initial release with basic functionality
- **v1.1.0**: Added motion detection and recording
- **v1.2.0**: Interactive map and mobile support
- **v1.3.0**: Multi-language support and improved UI

### Planned Features
- [ ] AI-powered object detection
- [ ] Cloud storage integration
- [ ] Mobile app
- [ ] Advanced analytics
- [ ] Email/SMS notifications
- [ ] Multi-tenant support

---

**Built with ❤️ using Python Flask and modern web technologies**
