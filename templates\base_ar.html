<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام المراقبة{% endblock %}</title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Leaflet CSS for maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style_ar.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    {% if current_user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-video"></i> نظام المراقبة
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('cameras') }}">
                            <i class="fas fa-camera"></i> الكاميرات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('map_view') }}">
                            <i class="fas fa-map-marker-alt"></i> الخريطة
                        </a>
                    </li>
                    {% if current_user.has_permission('manage') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('add_camera') }}">
                            <i class="fas fa-plus"></i> إضافة كاميرا
                        </a>
                    </li>
                    {% endif %}
                    {% if current_user.has_permission('admin') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.users') }}">
                            <i class="fas fa-users"></i> المستخدمين
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="fas fa-user-cog"></i> الملف الشخصي
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="toggleLanguage()">
                            <i class="fas fa-language"></i> EN
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Flash Messages -->
    <div class="container-fluid mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>
    
    <!-- Main Content -->
    <main class="container-fluid">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-light text-center py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 نظام المراقبة. جميع الحقوق محفوظة.</p>
            <p class="small">
                <a href="#" class="text-light me-3">الشروط والأحكام</a>
                <a href="#" class="text-light me-3">سياسة الخصوصية</a>
                <a href="#" class="text-light">اتصل بنا</a>
            </p>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="{{ url_for('static', filename='js/main_ar.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
    
    <script>
    // Language toggle function
    function toggleLanguage() {
        const currentLang = document.documentElement.lang;
        const newLang = currentLang === 'ar' ? 'en' : 'ar';
        
        // Store language preference
        localStorage.setItem('preferred_language', newLang);
        
        // Reload page with new language
        const url = new URL(window.location);
        url.searchParams.set('lang', newLang);
        window.location.href = url.toString();
    }
    
    // Set language on page load
    document.addEventListener('DOMContentLoaded', function() {
        const preferredLang = localStorage.getItem('preferred_language') || 'ar';
        const currentLang = document.documentElement.lang;
        
        if (preferredLang !== currentLang && !window.location.search.includes('lang=')) {
            const url = new URL(window.location);
            url.searchParams.set('lang', preferredLang);
            window.location.href = url.toString();
        }
    });
    </script>
</body>
</html>
