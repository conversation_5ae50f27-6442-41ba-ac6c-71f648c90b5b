{% extends "base.html" %}

{% block title %}Page Not Found - Surveillance System{% endblock %}

{% block content %}
<div class="container-fluid d-flex align-items-center justify-content-center" style="min-height: 60vh;">
    <div class="text-center">
        <div class="error-icon mb-4">
            <i class="fas fa-search fa-5x text-muted"></i>
        </div>
        
        <h1 class="display-1 fw-bold text-primary">404</h1>
        <h2 class="mb-4">Page Not Found</h2>
        
        <p class="lead text-muted mb-4">
            The page you're looking for doesn't exist or has been moved.
        </p>
        
        <div class="d-flex justify-content-center gap-3">
            <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                <i class="fas fa-home"></i> Go to Dashboard
            </a>
            <button onclick="history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Go Back
            </button>
        </div>
        
        <div class="mt-5">
            <h5>Quick Links:</h5>
            <div class="list-group list-group-horizontal justify-content-center">
                <a href="{{ url_for('cameras') }}" class="list-group-item list-group-item-action">
                    <i class="fas fa-camera"></i> Cameras
                </a>
                <a href="{{ url_for('map_view') }}" class="list-group-item list-group-item-action">
                    <i class="fas fa-map-marker-alt"></i> Map
                </a>
                {% if current_user.has_permission('manage') %}
                <a href="{{ url_for('add_camera') }}" class="list-group-item list-group-item-action">
                    <i class="fas fa-plus"></i> Add Camera
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.error-icon {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.list-group-horizontal .list-group-item {
    border-radius: 10px;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.list-group-horizontal .list-group-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
</style>
{% endblock %}
