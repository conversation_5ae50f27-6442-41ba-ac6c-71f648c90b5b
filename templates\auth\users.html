{% extends "base.html" %}

{% block title %}User Management - Surveillance System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-users"></i> User Management
            </h1>
            <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Add User
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> System Users
                </h5>
            </div>
            <div class="card-body">
                {% if users %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>User</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Last Login</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <strong>{{ user.username }}</strong>
                                            <br><small class="text-muted">{{ user.email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if user.role.value == 'admin' else 'warning' if user.role.value == 'manager' else 'info' }}">
                                        {{ user.role.value.title() }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if user.is_active else 'danger' }}">
                                        {{ 'Active' if user.is_active else 'Inactive' }}
                                    </span>
                                    {% if user.login_attempts > 0 %}
                                    <br><small class="text-warning">{{ user.login_attempts }} failed attempts</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.last_login %}
                                        <small>{{ user.last_login.strftime('%Y-%m-%d %H:%M') }}</small>
                                    {% else %}
                                        <span class="text-muted">Never</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ user.created_at.strftime('%Y-%m-%d') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        {% if user.id != current_user.id %}
                                        <!-- Toggle Status -->
                                        <button class="btn btn-{{ 'warning' if user.is_active else 'success' }}" 
                                                onclick="toggleUserStatus({{ user.id }}, '{{ user.username }}')"
                                                title="{{ 'Deactivate' if user.is_active else 'Activate' }} User">
                                            <i class="fas fa-{{ 'pause' if user.is_active else 'play' }}"></i>
                                        </button>
                                        
                                        <!-- Change Role -->
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-info dropdown-toggle" 
                                                    data-bs-toggle="dropdown" title="Change Role">
                                                <i class="fas fa-user-tag"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="changeUserRole({{ user.id }}, 'admin')">
                                                    <i class="fas fa-crown text-danger"></i> Admin
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="changeUserRole({{ user.id }}, 'manager')">
                                                    <i class="fas fa-user-tie text-warning"></i> Manager
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="changeUserRole({{ user.id }}, 'viewer')">
                                                    <i class="fas fa-eye text-info"></i> Viewer
                                                </a></li>
                                            </ul>
                                        </div>
                                        {% else %}
                                        <span class="badge bg-secondary">Current User</span>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No users found</h5>
                    <p class="text-muted">Add your first user to get started</p>
                    <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Add User
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ users|selectattr('role.value', 'equalto', 'admin')|list|length }}</h3>
                <p class="mb-0">Administrators</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>{{ users|selectattr('role.value', 'equalto', 'manager')|list|length }}</h3>
                <p class="mb-0">Managers</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>{{ users|selectattr('role.value', 'equalto', 'viewer')|list|length }}</h3>
                <p class="mb-0">Viewers</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>{{ users|selectattr('is_active', 'equalto', true)|list|length }}</h3>
                <p class="mb-0">Active Users</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleUserStatus(userId, username) {
    const action = event.target.closest('button').classList.contains('btn-warning') ? 'deactivate' : 'activate';
    
    if (confirm(`Are you sure you want to ${action} user "${username}"?`)) {
        fetch(`/auth/users/${userId}/toggle_status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Failed to update user status', 'danger');
        });
    }
}

function changeUserRole(userId, newRole) {
    if (confirm(`Are you sure you want to change this user's role to ${newRole}?`)) {
        fetch(`/auth/users/${userId}/change_role`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ role: newRole })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Failed to change user role', 'danger');
        });
    }
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}
</style>
{% endblock %}
