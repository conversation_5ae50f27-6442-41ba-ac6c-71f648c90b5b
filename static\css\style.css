/* Custom CSS for Surveillance System */

/* General Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Camera Grid Styles */
.camera-grid {
    display: grid;
    gap: 15px;
    padding: 15px 0;
}

.grid-1x1 {
    grid-template-columns: 1fr;
}

.grid-2x2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-3x3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-4x4 {
    grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
    .camera-grid {
        grid-template-columns: 1fr !important;
    }
}

.camera-item {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.camera-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.camera-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.camera-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.camera-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.camera-video {
    position: relative;
    background: #000;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.camera-stream {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: cover;
}

.camera-offline {
    color: #6c757d;
    text-align: center;
    padding: 40px 20px;
}

.camera-offline i {
    margin-bottom: 10px;
}

.camera-info {
    padding: 10px 15px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* Status Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
    padding: 1rem 1.5rem;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table-dark {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover > tbody > tr:hover > td {
    background-color: rgba(102, 126, 234, 0.1);
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.5rem;
}

.alert-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
}

/* Map Styles */
#map {
    height: 500px;
    border-radius: 15px;
    overflow: hidden;
}

.leaflet-popup-content {
    text-align: center;
}

.leaflet-popup-content h6 {
    margin-bottom: 10px;
    color: #495057;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 576px) {
    .camera-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .camera-controls {
        justify-content: center;
    }
    
    .btn-group {
        flex-wrap: wrap;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .card {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .table {
        color: #e9ecef;
    }
    
    .table-striped > tbody > tr:nth-of-type(odd) > td {
        background-color: rgba(255, 255, 255, 0.05);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-active {
    background-color: #28a745;
    box-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
}

.status-inactive {
    background-color: #6c757d;
}

.status-error {
    background-color: #dc3545;
    box-shadow: 0 0 5px rgba(220, 53, 69, 0.5);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
