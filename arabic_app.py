#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المراقبة باللغة العربية - نسخة مبسطة
Arabic Surveillance System - Simplified Version
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'arabic-surveillance-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///arabic_surveillance.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

# نموذج المستخدم
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='viewer')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

# نموذج الكاميرا
class Camera(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    camera_type = db.Column(db.String(20), nullable=False)
    connection_string = db.Column(db.String(500), nullable=False)
    location_name = db.Column(db.String(200))
    status = db.Column(db.String(20), default='inactive')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# قالب تسجيل الدخول
LOGIN_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المراقبة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .form-control { text-align: right; }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-header">
            <h2>🎥 نظام المراقبة</h2>
            <p>يرجى تسجيل الدخول للمتابعة</p>
        </div>
        <div class="p-4">
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-danger">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" name="username" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" class="form-control" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">تسجيل الدخول</button>
            </form>
            
            <div class="alert alert-info mt-3">
                <strong>بيانات الدخول الافتراضية:</strong><br>
                المستخدم: admin<br>
                كلمة المرور: admin123
            </div>
        </div>
    </div>
</body>
</html>
"""

# قالب لوحة التحكم
DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام المراقبة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border: none; border-radius: 15px; box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08); }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0 !important; }
        .stat-card { transition: transform 0.3s ease; }
        .stat-card:hover { transform: translateY(-5px); }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-video"></i> نظام المراقبة
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('cameras') }}">
                    <i class="fas fa-camera"></i> الكاميرات
                </a>
                <a class="nav-link" href="{{ url_for('add_camera') }}">
                    <i class="fas fa-plus"></i> إضافة كاميرا
                </a>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <h1 class="mb-4"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white stat-card">
                    <div class="card-body text-center">
                        <h3>{{ total_cameras }}</h3>
                        <p class="mb-0">إجمالي الكاميرات</p>
                        <i class="fas fa-camera fa-2x mt-2"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white stat-card">
                    <div class="card-body text-center">
                        <h3>{{ active_cameras }}</h3>
                        <p class="mb-0">الكاميرات النشطة</p>
                        <i class="fas fa-play-circle fa-2x mt-2"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white stat-card">
                    <div class="card-body text-center">
                        <h3>{{ offline_cameras }}</h3>
                        <p class="mb-0">الكاميرات غير المتصلة</p>
                        <i class="fas fa-pause-circle fa-2x mt-2"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white stat-card">
                    <div class="card-body text-center">
                        <h3>{{ current_user.username }}</h3>
                        <p class="mb-0">المستخدم الحالي</p>
                        <i class="fas fa-user fa-2x mt-2"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-video"></i> الكاميرات المتاحة</h5>
                    </div>
                    <div class="card-body">
                        {% if cameras %}
                            <div class="row">
                                {% for camera in cameras %}
                                <div class="col-md-4 mb-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ camera.name }}</h6>
                                            <p class="card-text">
                                                <small class="text-muted">
                                                    النوع: {{ camera.camera_type }}<br>
                                                    الموقع: {{ camera.location_name or 'غير محدد' }}<br>
                                                    الحالة: 
                                                    <span class="badge bg-{{ 'success' if camera.status == 'active' else 'secondary' }}">
                                                        {{ 'نشط' if camera.status == 'active' else 'غير نشط' }}
                                                    </span>
                                                </small>
                                            </p>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-primary btn-sm">
                                                    <i class="fas fa-eye"></i> عرض
                                                </button>
                                                <button class="btn btn-info btn-sm">
                                                    <i class="fas fa-camera"></i> صورة
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد كاميرات</h5>
                                <p class="text-muted">أضف أول كاميرا لبدء المراقبة</p>
                                <a href="{{ url_for('add_camera') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة كاميرا
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

# قالب إضافة كاميرا
ADD_CAMERA_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة كاميرا - نظام المراقبة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border: none; border-radius: 15px; box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08); }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0 !important; }
        .form-control { text-align: right; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-video"></i> نظام المراقبة
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a>
                <a class="nav-link" href="{{ url_for('cameras') }}">
                    <i class="fas fa-camera"></i> الكاميرات
                </a>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1 class="mb-4"><i class="fas fa-plus"></i> إضافة كاميرا جديدة</h1>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cog"></i> تكوين الكاميرا</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">اسم الكاميرا *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">نوع الكاميرا *</label>
                                        <select class="form-select" name="camera_type" required>
                                            <option value="">اختر نوع الكاميرا</option>
                                            <option value="rtsp">RTSP</option>
                                            <option value="usb">USB</option>
                                            <option value="dahua">داهوا</option>
                                            <option value="hikvision">هيكفيجن</option>
                                            <option value="onvif">ONVIF</option>
                                            <option value="http">HTTP</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">سلسلة الاتصال *</label>
                                <input type="text" class="form-control" name="connection_string" required>
                                <div class="form-text">مثال: rtsp://admin:password@*************:554/stream1</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">اسم الموقع</label>
                                <input type="text" class="form-control" name="location_name" placeholder="مثال: المدخل الرئيسي">
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('cameras') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> العودة
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> إضافة الكاميرا
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle"></i> أمثلة الاتصال</h5>
                    </div>
                    <div class="card-body">
                        <h6>RTSP:</h6>
                        <code>rtsp://admin:pass@*************:554/stream1</code>
                        
                        <h6 class="mt-3">USB:</h6>
                        <code>0</code> (رقم الجهاز)
                        
                        <h6 class="mt-3">داهوا:</h6>
                        <code>rtsp://admin:pass@ip:554/cam/realmonitor?channel=1&subtype=0</code>
                        
                        <h6 class="mt-3">هيكفيجن:</h6>
                        <code>rtsp://admin:pass@ip:554/Streaming/Channels/101</code>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

# الصفحات
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    cameras = Camera.query.all()
    total_cameras = len(cameras)
    active_cameras = len([c for c in cameras if c.status == 'active'])
    offline_cameras = total_cameras - active_cameras
    
    return render_template_string(DASHBOARD_TEMPLATE, 
                                cameras=cameras,
                                total_cameras=total_cameras,
                                active_cameras=active_cameras,
                                offline_cameras=offline_cameras)

@app.route('/cameras')
@login_required
def cameras():
    cameras = Camera.query.all()
    return render_template_string(DASHBOARD_TEMPLATE, 
                                cameras=cameras,
                                total_cameras=len(cameras),
                                active_cameras=len([c for c in cameras if c.status == 'active']),
                                offline_cameras=len([c for c in cameras if c.status != 'active']))

@app.route('/add_camera', methods=['GET', 'POST'])
@login_required
def add_camera():
    if request.method == 'POST':
        try:
            camera = Camera(
                name=request.form['name'],
                camera_type=request.form['camera_type'],
                connection_string=request.form['connection_string'],
                location_name=request.form.get('location_name', '')
            )
            
            db.session.add(camera)
            db.session.commit()
            
            flash('تم إضافة الكاميرا بنجاح!', 'success')
            return redirect(url_for('dashboard'))
            
        except Exception as e:
            db.session.rollback()
            flash('فشل في إضافة الكاميرا. يرجى المحاولة مرة أخرى.', 'error')
    
    return render_template_string(ADD_CAMERA_TEMPLATE)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي
        if User.query.count() == 0:
            admin = User(username='admin', role='admin')
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي: admin / admin123")
    
    print("🎥 نظام المراقبة العربي")
    print("=" * 40)
    print("📍 الخادم: http://localhost:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("🌐 اللغة: العربية")
    print("=" * 40)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
