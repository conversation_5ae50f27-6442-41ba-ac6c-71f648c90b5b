// Main JavaScript for Arabic Surveillance System
// ملف JavaScript الرئيسي لنظام المراقبة باللغة العربية

// Arabic translations
const arabicTexts = {
    // Camera controls
    'camera_started': 'تم تشغيل الكاميرا بنجاح',
    'camera_stopped': 'تم إيقاف الكاميرا بنجاح',
    'snapshot_taken': 'تم التقاط الصورة بنجاح',
    'camera_deleted': 'تم حذف الكاميرا بنجاح',
    'failed_start_camera': 'فشل في تشغيل الكاميرا',
    'failed_stop_camera': 'فشل في إيقاف الكاميرا',
    'failed_snapshot': 'فشل في التقاط الصورة',
    'failed_delete_camera': 'فشل في حذف الكاميرا',
    
    // Confirmations
    'confirm_delete_camera': 'هل أنت متأكد من حذف الكاميرا "{name}"؟ لا يمكن التراجع عن هذا الإجراء.',
    'confirm_stop_camera': 'هل تريد إيقاف الكاميرا؟',
    'confirm_start_camera': 'هل تريد تشغيل الكاميرا؟',
    
    // Status messages
    'connected_to_system': 'متصل بنظام المراقبة',
    'disconnected_from_system': 'منقطع عن نظام المراقبة',
    'connection_restored': 'تم استعادة الاتصال',
    'connection_lost': 'فقدان الاتصال',
    'motion_detected': 'تم اكتشاف حركة في {camera}',
    'recording_started': 'بدأ التسجيل في {camera}',
    'recording_stopped': 'توقف التسجيل في {camera}',
    
    // General
    'loading': 'جاري التحميل...',
    'please_wait': 'يرجى الانتظار...',
    'error_occurred': 'حدث خطأ غير متوقع',
    'try_again': 'حاول مرة أخرى',
    'success': 'تم بنجاح',
    'failed': 'فشل',
    'copied_to_clipboard': 'تم النسخ للحافظة',
    'failed_copy': 'فشل في النسخ للحافظة',
    
    // Time
    'seconds_ago': 'منذ {count} ثانية',
    'minutes_ago': 'منذ {count} دقيقة',
    'hours_ago': 'منذ {count} ساعة',
    'days_ago': 'منذ {count} يوم',
    'just_now': 'الآن',
    
    // Camera status
    'camera_online': 'الكاميرا متصلة',
    'camera_offline': 'الكاميرا غير متصلة',
    'camera_error': 'خطأ في الكاميرا',
    'camera_connecting': 'جاري الاتصال بالكاميرا'
};

// Initialize Socket.IO connection
let socket;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Socket.IO if available
    if (typeof io !== 'undefined') {
        initializeSocket();
    }
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize auto-refresh
    initializeAutoRefresh();
    
    // Add fade-in animation to cards
    addFadeInAnimation();
    
    // Initialize Arabic number formatting
    formatArabicNumbers();
    
    // Initialize RTL date formatting
    initializeRTLDates();
});

function initializeSocket() {
    socket = io();
    
    socket.on('connect', function() {
        console.log('Connected to surveillance system');
        showNotification(arabicTexts.connected_to_system, 'success');
    });
    
    socket.on('disconnect', function() {
        console.log('Disconnected from surveillance system');
        showNotification(arabicTexts.disconnected_from_system, 'warning');
    });
    
    socket.on('camera_status_update', function(data) {
        updateCameraStatus(data);
    });
    
    socket.on('motion_detected', function(data) {
        handleMotionDetection(data);
    });
    
    socket.on('recording_started', function(data) {
        handleRecordingStarted(data);
    });
    
    socket.on('recording_stopped', function(data) {
        handleRecordingStopped(data);
    });
    
    // Request initial camera status
    socket.emit('camera_status_request');
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function initializeAutoRefresh() {
    // Auto-refresh camera status every 30 seconds
    setInterval(function() {
        if (socket && socket.connected) {
            socket.emit('camera_status_request');
        }
    }, 30000);
}

function addFadeInAnimation() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
}

function formatArabicNumbers() {
    // Convert English numbers to Arabic numbers
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    
    document.querySelectorAll('.arabic-numbers').forEach(element => {
        element.textContent = element.textContent.replace(/\d/g, function(match) {
            return arabicNumbers[parseInt(match)];
        });
    });
}

function initializeRTLDates() {
    // Format dates for RTL display
    document.querySelectorAll('.date-rtl').forEach(element => {
        const date = new Date(element.textContent);
        if (!isNaN(date)) {
            element.textContent = date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    });
}

function updateCameraStatus(statusData) {
    Object.keys(statusData).forEach(cameraId => {
        const status = statusData[cameraId];
        const cameraElement = document.querySelector(`[data-camera-id="${cameraId}"]`);
        
        if (cameraElement) {
            // Update status badge
            const statusBadge = cameraElement.querySelector('.badge');
            if (statusBadge) {
                statusBadge.className = `badge bg-${getStatusColor(status.status)}`;
                statusBadge.textContent = getStatusTextArabic(status.status);
            }
            
            // Update last seen
            const lastSeenElement = cameraElement.querySelector('.last-seen');
            if (lastSeenElement && status.last_seen) {
                lastSeenElement.textContent = formatDateTimeArabic(status.last_seen);
            }
            
            // Update camera stream if status changed to active
            if (status.status === 'active') {
                const streamImg = cameraElement.querySelector('.camera-stream');
                if (streamImg) {
                    streamImg.src = `/video_feed/${cameraId}?t=${Date.now()}`;
                }
            }
        }
    });
}

function getStatusColor(status) {
    switch (status) {
        case 'active': return 'success';
        case 'error': return 'danger';
        case 'connecting': return 'warning';
        default: return 'secondary';
    }
}

function getStatusTextArabic(status) {
    switch (status) {
        case 'active': return 'نشط';
        case 'inactive': return 'غير نشط';
        case 'error': return 'خطأ';
        case 'connecting': return 'جاري الاتصال';
        default: return 'غير معروف';
    }
}

function formatDateTimeArabic(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function handleMotionDetection(data) {
    const { camera_id, camera_name, timestamp } = data;
    
    // Show notification
    const message = arabicTexts.motion_detected.replace('{camera}', camera_name);
    showNotification(message, 'warning', 5000);
    
    // Add visual indicator
    const cameraElement = document.querySelector(`[data-camera-id="${camera_id}"]`);
    if (cameraElement) {
        cameraElement.classList.add('motion-detected');
        setTimeout(() => {
            cameraElement.classList.remove('motion-detected');
        }, 3000);
    }
    
    // Play notification sound (if enabled)
    playNotificationSound();
}

function handleRecordingStarted(data) {
    const { camera_id, camera_name } = data;
    const message = arabicTexts.recording_started.replace('{camera}', camera_name);
    showNotification(message, 'info');
    
    // Update recording indicator
    updateRecordingIndicator(camera_id, true);
}

function handleRecordingStopped(data) {
    const { camera_id, camera_name } = data;
    const message = arabicTexts.recording_stopped.replace('{camera}', camera_name);
    showNotification(message, 'info');
    
    // Update recording indicator
    updateRecordingIndicator(camera_id, false);
}

function updateRecordingIndicator(cameraId, isRecording) {
    const cameraElement = document.querySelector(`[data-camera-id="${cameraId}"]`);
    if (cameraElement) {
        const recordingIndicator = cameraElement.querySelector('.recording-indicator');
        if (recordingIndicator) {
            recordingIndicator.style.display = isRecording ? 'inline-block' : 'none';
        }
    }
}

function showNotification(message, type = 'info', duration = 3000) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show notification`;
    notification.style.direction = 'rtl';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to notification container or create one
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
            max-width: 400px;
            direction: rtl;
        `;
        document.body.appendChild(container);
    }
    
    container.appendChild(notification);
    
    // Auto-remove after duration
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

function playNotificationSound() {
    // Create and play notification sound
    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
    audio.volume = 0.3;
    audio.play().catch(e => console.log('Could not play notification sound:', e));
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '٠ بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification(arabicTexts.copied_to_clipboard, 'success', 2000);
    }).catch(err => {
        console.error('Could not copy text: ', err);
        showNotification(arabicTexts.failed_copy, 'error', 2000);
    });
}

// Camera control functions (used across multiple pages)
window.cameraControls = {
    start: function(cameraId) {
        return fetch(`/cameras/${cameraId}/start`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => response.json());
    },
    
    stop: function(cameraId) {
        return fetch(`/cameras/${cameraId}/stop`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => response.json());
    },
    
    snapshot: function(cameraId) {
        return fetch(`/api/cameras/${cameraId}/snapshot`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => response.json());
    },
    
    delete: function(cameraId) {
        return fetch(`/cameras/${cameraId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => response.json());
    }
};

// Global error handler
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    showNotification(arabicTexts.error_occurred, 'danger');
});

// Handle offline/online status
window.addEventListener('online', function() {
    showNotification(arabicTexts.connection_restored, 'success');
    if (socket && !socket.connected) {
        socket.connect();
    }
});

window.addEventListener('offline', function() {
    showNotification(arabicTexts.connection_lost, 'warning');
});

// Add CSS for motion detection animation
const style = document.createElement('style');
style.textContent = `
    .motion-detected {
        animation: motionPulse 1s ease-in-out infinite;
        border: 2px solid #ffc107 !important;
    }
    
    @keyframes motionPulse {
        0%, 100% { 
            box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
        }
        50% { 
            box-shadow: 0 0 20px rgba(255, 193, 7, 0.8);
        }
    }
    
    .notification {
        margin-bottom: 10px;
        animation: slideInLeft 0.3s ease-out;
    }
    
    @keyframes slideInLeft {
        from {
            transform: translateX(-100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
