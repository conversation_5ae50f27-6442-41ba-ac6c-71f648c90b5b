# Surveillance System Environment Configuration
# Copy this file to .env and update the values

# Flask Configuration
SECRET_KEY=your-secret-key-change-this-in-production
FLASK_ENV=development

# Database Configuration
DATABASE_URL=sqlite:///surveillance.db

# Email Configuration (Optional - for notifications)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Camera Configuration
MAX_CAMERAS=50
STREAM_TIMEOUT=30
VIDEO_WIDTH=640
VIDEO_HEIGHT=480
VIDEO_FPS=30

# Motion Detection
MOTION_DETECTION_ENABLED=true
MOTION_THRESHOLD=500

# Map Configuration (Default: Riyadh, Saudi Arabia)
DEFAULT_MAP_CENTER_LAT=24.7136
DEFAULT_MAP_CENTER_LNG=46.6753
DEFAULT_MAP_ZOOM=10

# Security
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5

# File Upload
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads
