{% extends "base.html" %}

{% block title %}Profile - Surveillance System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-user"></i> User Profile
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-cog"></i> Profile Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Username:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.username }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Email:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.email }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Role:</strong>
                    </div>
                    <div class="col-sm-9">
                        <span class="badge bg-primary">{{ user.role.value.title() }}</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Account Status:</strong>
                    </div>
                    <div class="col-sm-9">
                        <span class="badge bg-{{ 'success' if user.is_active else 'danger' }}">
                            {{ 'Active' if user.is_active else 'Inactive' }}
                        </span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Member Since:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.created_at.strftime('%B %d, %Y') }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Last Login:</strong>
                    </div>
                    <div class="col-sm-9">
                        {% if user.last_login %}
                            {{ user.last_login.strftime('%B %d, %Y at %I:%M %p') }}
                        {% else %}
                            <span class="text-muted">Never</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-key"></i> Change Password
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.change_password') }}">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Change Password
                    </button>
                </form>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt"></i> Security Tips
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        Use a strong password with at least 8 characters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        Include uppercase, lowercase, numbers, and symbols
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        Don't reuse passwords from other accounts
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success"></i>
                        Change your password regularly
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
