// Main JavaScript for Surveillance System

// Initialize Socket.IO connection
let socket;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Socket.IO if available
    if (typeof io !== 'undefined') {
        initializeSocket();
    }
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize auto-refresh
    initializeAutoRefresh();
    
    // Add fade-in animation to cards
    addFadeInAnimation();
});

function initializeSocket() {
    socket = io();
    
    socket.on('connect', function() {
        console.log('Connected to surveillance system');
        showNotification('Connected to surveillance system', 'success');
    });
    
    socket.on('disconnect', function() {
        console.log('Disconnected from surveillance system');
        showNotification('Disconnected from surveillance system', 'warning');
    });
    
    socket.on('camera_status_update', function(data) {
        updateCameraStatus(data);
    });
    
    socket.on('motion_detected', function(data) {
        handleMotionDetection(data);
    });
    
    socket.on('recording_started', function(data) {
        handleRecordingStarted(data);
    });
    
    socket.on('recording_stopped', function(data) {
        handleRecordingStopped(data);
    });
    
    // Request initial camera status
    socket.emit('camera_status_request');
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function initializeAutoRefresh() {
    // Auto-refresh camera status every 30 seconds
    setInterval(function() {
        if (socket && socket.connected) {
            socket.emit('camera_status_request');
        }
    }, 30000);
}

function addFadeInAnimation() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
}

function updateCameraStatus(statusData) {
    Object.keys(statusData).forEach(cameraId => {
        const status = statusData[cameraId];
        const cameraElement = document.querySelector(`[data-camera-id="${cameraId}"]`);
        
        if (cameraElement) {
            // Update status badge
            const statusBadge = cameraElement.querySelector('.badge');
            if (statusBadge) {
                statusBadge.className = `badge bg-${getStatusColor(status.status)}`;
                statusBadge.textContent = status.status.charAt(0).toUpperCase() + status.status.slice(1);
            }
            
            // Update last seen
            const lastSeenElement = cameraElement.querySelector('.last-seen');
            if (lastSeenElement && status.last_seen) {
                lastSeenElement.textContent = formatDateTime(status.last_seen);
            }
            
            // Update camera stream if status changed to active
            if (status.status === 'active') {
                const streamImg = cameraElement.querySelector('.camera-stream');
                if (streamImg) {
                    streamImg.src = `/video_feed/${cameraId}?t=${Date.now()}`;
                }
            }
        }
    });
}

function getStatusColor(status) {
    switch (status) {
        case 'active': return 'success';
        case 'error': return 'danger';
        case 'connecting': return 'warning';
        default: return 'secondary';
    }
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
}

function handleMotionDetection(data) {
    const { camera_id, camera_name, timestamp } = data;
    
    // Show notification
    showNotification(`Motion detected on ${camera_name}`, 'warning', 5000);
    
    // Add visual indicator
    const cameraElement = document.querySelector(`[data-camera-id="${camera_id}"]`);
    if (cameraElement) {
        cameraElement.classList.add('motion-detected');
        setTimeout(() => {
            cameraElement.classList.remove('motion-detected');
        }, 3000);
    }
    
    // Play notification sound (if enabled)
    playNotificationSound();
}

function handleRecordingStarted(data) {
    const { camera_id, camera_name } = data;
    showNotification(`Recording started on ${camera_name}`, 'info');
    
    // Update recording indicator
    updateRecordingIndicator(camera_id, true);
}

function handleRecordingStopped(data) {
    const { camera_id, camera_name } = data;
    showNotification(`Recording stopped on ${camera_name}`, 'info');
    
    // Update recording indicator
    updateRecordingIndicator(camera_id, false);
}

function updateRecordingIndicator(cameraId, isRecording) {
    const cameraElement = document.querySelector(`[data-camera-id="${cameraId}"]`);
    if (cameraElement) {
        const recordingIndicator = cameraElement.querySelector('.recording-indicator');
        if (recordingIndicator) {
            recordingIndicator.style.display = isRecording ? 'inline-block' : 'none';
        }
    }
}

function showNotification(message, type = 'info', duration = 3000) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show notification`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to notification container or create one
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
    
    container.appendChild(notification);
    
    // Auto-remove after duration
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

function playNotificationSound() {
    // Create and play notification sound
    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
    audio.volume = 0.3;
    audio.play().catch(e => console.log('Could not play notification sound:', e));
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Copied to clipboard', 'success', 2000);
    }).catch(err => {
        console.error('Could not copy text: ', err);
        showNotification('Failed to copy to clipboard', 'error', 2000);
    });
}

// Camera control functions (used across multiple pages)
window.cameraControls = {
    start: function(cameraId) {
        return fetch(`/cameras/${cameraId}/start`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => response.json());
    },
    
    stop: function(cameraId) {
        return fetch(`/cameras/${cameraId}/stop`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => response.json());
    },
    
    snapshot: function(cameraId) {
        return fetch(`/api/cameras/${cameraId}/snapshot`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => response.json());
    },
    
    delete: function(cameraId) {
        return fetch(`/cameras/${cameraId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => response.json());
    }
};

// Global error handler
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    showNotification('An unexpected error occurred', 'danger');
});

// Handle offline/online status
window.addEventListener('online', function() {
    showNotification('Connection restored', 'success');
    if (socket && !socket.connected) {
        socket.connect();
    }
});

window.addEventListener('offline', function() {
    showNotification('Connection lost', 'warning');
});

// Add CSS for motion detection animation
const style = document.createElement('style');
style.textContent = `
    .motion-detected {
        animation: motionPulse 1s ease-in-out infinite;
        border: 2px solid #ffc107 !important;
    }
    
    @keyframes motionPulse {
        0%, 100% { 
            box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
        }
        50% { 
            box-shadow: 0 0 20px rgba(255, 193, 7, 0.8);
        }
    }
    
    .notification {
        margin-bottom: 10px;
        animation: slideInRight 0.3s ease-out;
    }
    
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
