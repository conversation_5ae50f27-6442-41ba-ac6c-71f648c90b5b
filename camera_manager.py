import cv2
import threading
import time
import numpy as np
from datetime import datetime
import os
import logging
from models import Camera, CameraStatus, CameraType, Recording, Snapshot, db
from config import Config
import requests
from onvif import ONVIFCamera
import imutils

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CameraStream:
    def __init__(self, camera_id):
        self.camera_id = camera_id
        self.camera = None
        self.cap = None
        self.frame = None
        self.is_running = False
        self.thread = None
        self.last_frame_time = time.time()
        self.motion_detector = MotionDetector()
        
    def start(self):
        """Start the camera stream"""
        if self.is_running:
            return False
            
        self.camera = Camera.query.get(self.camera_id)
        if not self.camera:
            logger.error(f"Camera {self.camera_id} not found")
            return False
            
        self.is_running = True
        self.thread = threading.Thread(target=self._capture_frames)
        self.thread.daemon = True
        self.thread.start()
        return True
    
    def stop(self):
        """Stop the camera stream"""
        self.is_running = False
        if self.thread:
            self.thread.join(timeout=5)
        if self.cap:
            self.cap.release()
        self._update_camera_status(CameraStatus.INACTIVE)
    
    def _capture_frames(self):
        """Capture frames from camera"""
        try:
            self.cap = self._initialize_camera()
            if not self.cap:
                self._update_camera_status(CameraStatus.ERROR, "Failed to initialize camera")
                return
                
            self._update_camera_status(CameraStatus.ACTIVE)
            
            while self.is_running:
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning(f"Failed to read frame from camera {self.camera_id}")
                    time.sleep(1)
                    continue
                
                # Resize frame for better performance
                frame = imutils.resize(frame, width=Config.VIDEO_WIDTH)
                
                # Motion detection
                if self.camera.motion_detection:
                    motion_detected = self.motion_detector.detect_motion(frame)
                    if motion_detected:
                        self._handle_motion_detection(frame)
                
                # Encode frame as JPEG
                ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                if ret:
                    self.frame = buffer.tobytes()
                    self.last_frame_time = time.time()
                
                time.sleep(1.0 / Config.VIDEO_FPS)
                
        except Exception as e:
            logger.error(f"Error in camera {self.camera_id}: {str(e)}")
            self._update_camera_status(CameraStatus.ERROR, str(e))
        finally:
            if self.cap:
                self.cap.release()
    
    def _initialize_camera(self):
        """Initialize camera based on type"""
        try:
            if self.camera.camera_type == CameraType.USB:
                # USB Camera
                device_id = int(self.camera.connection_string)
                cap = cv2.VideoCapture(device_id)
                
            elif self.camera.camera_type in [CameraType.RTSP, CameraType.DAHUA, CameraType.HIKVISION]:
                # RTSP Stream
                cap = cv2.VideoCapture(self.camera.connection_string)
                
            elif self.camera.camera_type == CameraType.HTTP:
                # HTTP/MJPEG Stream
                cap = cv2.VideoCapture(self.camera.connection_string)
                
            elif self.camera.camera_type == CameraType.ONVIF:
                # ONVIF Camera
                onvif_url = self._get_onvif_stream_url()
                if onvif_url:
                    cap = cv2.VideoCapture(onvif_url)
                else:
                    return None
            else:
                logger.error(f"Unsupported camera type: {self.camera.camera_type}")
                return None
            
            # Set camera properties
            if cap and cap.isOpened():
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, Config.VIDEO_WIDTH)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, Config.VIDEO_HEIGHT)
                cap.set(cv2.CAP_PROP_FPS, Config.VIDEO_FPS)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Reduce buffer to minimize delay
                return cap
            else:
                logger.error(f"Failed to open camera {self.camera_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error initializing camera {self.camera_id}: {str(e)}")
            return None
    
    def _get_onvif_stream_url(self):
        """Get RTSP URL from ONVIF camera"""
        try:
            mycam = ONVIFCamera(
                self.camera.ip_address,
                self.camera.port or 80,
                self.camera.username,
                self.camera.password
            )
            
            media_service = mycam.create_media_service()
            profiles = media_service.GetProfiles()
            
            if profiles:
                stream_uri = media_service.GetStreamUri({
                    'StreamSetup': {
                        'Stream': 'RTP-Unicast',
                        'Transport': {'Protocol': 'RTSP'}
                    },
                    'ProfileToken': profiles[0].token
                })
                return stream_uri.Uri
            return None
            
        except Exception as e:
            logger.error(f"ONVIF error for camera {self.camera_id}: {str(e)}")
            return None
    
    def _update_camera_status(self, status, error_message=None):
        """Update camera status in database"""
        try:
            camera = Camera.query.get(self.camera_id)
            if camera:
                camera.status = status
                camera.last_seen = datetime.utcnow()
                if error_message:
                    camera.error_message = error_message
                db.session.commit()
        except Exception as e:
            logger.error(f"Error updating camera status: {str(e)}")
    
    def _handle_motion_detection(self, frame):
        """Handle motion detection event"""
        try:
            # Save snapshot
            self._save_snapshot(frame, "motion")
            
            # Start recording if enabled
            if self.camera.is_recording:
                self._start_recording()
                
        except Exception as e:
            logger.error(f"Error handling motion detection: {str(e)}")
    
    def _save_snapshot(self, frame, trigger_type="manual"):
        """Save snapshot to disk"""
        try:
            if not os.path.exists(Config.SNAPSHOT_PATH):
                os.makedirs(Config.SNAPSHOT_PATH)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"camera_{self.camera_id}_{timestamp}.jpg"
            filepath = os.path.join(Config.SNAPSHOT_PATH, filename)
            
            cv2.imwrite(filepath, frame)
            
            # Save to database
            snapshot = Snapshot(
                camera_id=self.camera_id,
                filename=filename,
                file_path=filepath,
                file_size=os.path.getsize(filepath),
                width=frame.shape[1],
                height=frame.shape[0],
                trigger_type=trigger_type
            )
            db.session.add(snapshot)
            db.session.commit()
            
            logger.info(f"Snapshot saved: {filename}")
            
        except Exception as e:
            logger.error(f"Error saving snapshot: {str(e)}")
    
    def _start_recording(self):
        """Start video recording"""
        # This would be implemented for video recording
        # For now, just log the event
        logger.info(f"Recording started for camera {self.camera_id}")
    
    def get_frame(self):
        """Get the latest frame"""
        if self.frame is not None and time.time() - self.last_frame_time < Config.STREAM_TIMEOUT:
            return self.frame
        return None

class MotionDetector:
    def __init__(self):
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(detectShadows=True)
        self.min_area = 500
        
    def detect_motion(self, frame):
        """Detect motion in frame"""
        try:
            # Apply background subtraction
            fg_mask = self.background_subtractor.apply(frame)
            
            # Remove noise
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Check if any contour is large enough
            for contour in contours:
                if cv2.contourArea(contour) > self.min_area:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Motion detection error: {str(e)}")
            return False

class CameraManager:
    def __init__(self):
        self.streams = {}
        self.lock = threading.Lock()
    
    def start_camera(self, camera_id):
        """Start a camera stream"""
        with self.lock:
            if camera_id in self.streams:
                return True
            
            stream = CameraStream(camera_id)
            if stream.start():
                self.streams[camera_id] = stream
                logger.info(f"Camera {camera_id} started")
                return True
            return False
    
    def stop_camera(self, camera_id):
        """Stop a camera stream"""
        with self.lock:
            if camera_id in self.streams:
                self.streams[camera_id].stop()
                del self.streams[camera_id]
                logger.info(f"Camera {camera_id} stopped")
                return True
            return False
    
    def get_frame(self, camera_id):
        """Get frame from camera"""
        with self.lock:
            if camera_id in self.streams:
                return self.streams[camera_id].get_frame()
            return None
    
    def stop_all(self):
        """Stop all camera streams"""
        with self.lock:
            for camera_id in list(self.streams.keys()):
                self.stop_camera(camera_id)

# Global camera manager instance
camera_manager = CameraManager()
